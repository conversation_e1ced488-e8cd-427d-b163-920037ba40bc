{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:react/recommended", "plugin:react/jsx-runtime", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["react-hooks", "@typescript-eslint", "import", "jsx-a11y"], "rules": {"max-lines": ["error", {"max": 700, "skipBlankLines": true, "skipComments": true}], "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "warn"}, "overrides": [{"files": ["src/assets/**/*.*"], "rules": {"max-lines": "off"}}]}