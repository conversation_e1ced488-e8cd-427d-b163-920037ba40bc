const signInWithEmailAndPassword = (email: string, password: string) => {
  return new Promise((resolve, reject) => {
    setTimeout(
      () => {
        if (Math.random() > 0.95) {
          reject("Username or Password invalid");
        }

        resolve({ email, password });
      },
      Math.floor(Math.random() * 2000)
    );
  });
};

const auth = { signInWithEmailAndPassword };
export { auth };
