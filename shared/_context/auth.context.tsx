'use client';

import { createContext, PropsWithChildren, useState } from 'react';
import getEnv from '../_utils/hooks/getEnv';

export interface IAuthProviderData {
  customerId: string;
}

export interface IAuthProviderValue {
  authData: IAuthProviderData;
  setAuthData: (data: Partial<IAuthProviderData>) => void;
}

export const AuthContext = createContext<IAuthProviderValue>({} as any);

export interface IAuthProviderProps
  extends Partial<PropsWithChildren<JSX.IntrinsicElements['div']>> {
  initialValue?: IAuthProviderData;
}

export default function AuthProvider({
  children,
  initialValue,
}: IAuthProviderProps) {
  const initialData: IAuthProviderData = {
    customerId: getEnv('FORCE_CUSTOMER_ID') || '',

    ...(initialValue || {}),
  };

  const [authProviderData, setAuthProviderData] =
    useState<IAuthProviderData>(initialData);

  const handleSetAuthProviderData = (newData: Partial<IAuthProviderData>) => {
    setAuthProviderData({
      ...authProviderData,
      ...newData,
    });
  };

  return (
    <AuthContext.Provider
      value={{
        authData: authProviderData,
        setAuthData: handleSetAuthProviderData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
