"use client"

import {
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query'
import { PropsWithChildren } from 'react'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: Infinity,
      retry: false,
      refetchOnWindowFocus: false,
    },
  },
})

export interface IQueryProviderProps extends Partial<PropsWithChildren<JSX.IntrinsicElements["div"]>> {
}

export default function QueryProvider({ children }: IQueryProviderProps) {

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

