"use client";

import { PropsWithChildren } from "react";
import { twMerge } from "tailwind-merge";
import dynamic from "next/dynamic";
import { ApexOptions } from "apexcharts";
import { startCase } from "lodash";
const ReactApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

export interface ITotalInvestimentsWidgetProps extends Partial<PropsWithChildren<JSX.IntrinsicElements["div"]>> {}

interface spark3 {
  options?: ApexOptions;
  width?: number;
  height?: string | number;
  series?: ApexOptions["series"];
  label?: XAxisAnnotations;
  color?: string | string[] | (string & string[]) | undefined;
  endingShape?: string;
  enabled?: boolean;
}
const arrdt = [
  [1327359600000, 30.95],
  [1327618800000, 31.05],
  [1328050800000, 31.24],
  [1328482800000, 31.86],
  [1328742000000, 32.65],
  [1329174000000, 32.44],
  [1329433200000, 32.75],
  [1329951600000, 32.97],
  [1330383600000, 33.27],
  [1330642800000, 33.73],
  [1331074800000, 32.41],
  [1331506800000, 33.56],
  [1331766000000, 34.17],
  [1332198000000, 33.16],
  [1332457200000, 33.81],
  [1332885600000, 34.46],
  [1333317600000, 34.7],
  [1333576800000, 33.59],
  [1334095200000, 33.01],
  [1334527200000, 32.84],
  [1334786400000, 32.91],
  [1335218400000, 32.4],
  [1335477600000, 33.58],
  [1335909600000, 33.76],
  [1336341600000, 32.52],
  [1336600800000, 31.92],
  [1337032800000, 32.33],
  [1337292000000, 31.31],
  [1337724000000, 32.18],
  [1338242400000, 32.05],
  [1338501600000, 29.82],
  [1338933600000, 31.69],
  [1339365600000, 31.13],
  [1339624800000, 31.67],
  [1340056800000, 32.89],
  [1340316000000, 31.57],
  [1340748000000, 31.41],
  [1341180000000, 32.19],
  [1341525600000, 31.37],
  [1341957600000, 30.2],
  [1342389600000, 30.4],
  [1342648800000, 31.89],
  [1343080800000, 30.02],
  [1343340000000, 31.89],
  [1343772000000, 30.69],
  [1344204000000, 32.14],
  [1344463200000, 32.65],
  [1344895200000, 32.1],
  [1345154400000, 33.8],
  [1345586400000, 33.84],
  [1346018400000, 32.32],
  [1346277600000, 31.46],
  [1346796000000, 32.26],
  [1347228000000, 32.13],
  [1347487200000, 32.81],
  [1347919200000, 32.57],
  [1348178400000, 33.83],
  [1348610400000, 32.53],
  [1349042400000, 32.62],
  [1349301600000, 32.68],
  [1349733600000, 31.68],
  [1349992800000, 31.94],
  [1350424800000, 33.44],
  [1350856800000, 33.49],
  [1351116000000, 33.4],
  [1351724400000, 34.36],
  [1352156400000, 34.39],
  [1352415600000, 32.9],
  [1352847600000, 32.23],
  [1353279600000, 32.92],
  [1353625200000, 33.4],
  [1354057200000, 33.88],
  [1354489200000, 34.7],
  [1354748400000, 35.14],
  [1355180400000, 35.54],
  [1355439600000, 37.56],
  [1355871600000, 38.09],
  [1356303600000, 37.53],
  [1356649200000, 36.9],
  [1357167600000, 37.75],
  [1357599600000, 38.14],
  [1357858800000, 38.09],
  [1358290800000, 37.88],
  [1358809200000, 37.95],
  [1359068400000, 38.32],
  [1359500400000, 37.94],
  [1359932400000, 38.1],
  [1360191600000, 38.07],
  [1360623600000, 38.89],
  [1360882800000, 38.63],
  [1361401200000, 38.34],
  [1361919600000, 39.6],
];

const Totalinvestments: spark3 = {
  series: [
    ...[
      "PENDING",
      "IN_PROGRESS",
      "UNDER_REVIEW",
      "BLOCKED",
      "PAUSED",
      "STOPPED",
      "CANCELLED",
      "FINISHED",
      "UNLINKED",
      "IDLE",
    ].map((name) => ({
      name: startCase(name.toLowerCase()),
      data: arrdt.filter((_, i) => i % 7 === 0).map((x) => [x[0], Math.floor((Math.random() * 10 - 5 + x[1]) * 100)]),
    })),
  ],
  options: {
    chart: {
      events: {
        mounted: (chart) => {
          chart.windowResizeHandler();
        },
      },
      id: "area-datetime",
      fontFamily: "Roboto, Arial, sans-serif",
      type: "line",
      height: 320,
      zoom: {
        autoScaleYaxis: true,
      },
      toolbar: {
        show: false,
      },
    },
    grid: {
      borderColor: "#f3f3f3",
      strokeDashArray: 3,
    },
    dataLabels: {
      enabled: false,
    },
    markers: {
      size: 0,
      // style: 'hollow',
    },
    xaxis: {
      type: "datetime",
      min: new Date("01 Mar 2012").getTime(),
      tickAmount: 6,
    },
    tooltip: {
      x: {
        format: "dd MMM yyyy",
      },
    },
    colors: [
      "rgb(132,90,223)",
      "rgb(35,183,229)",
      "rgb(245,184,73)",
      "rgb(73,182,245)",
      "rgb(38,191,148)",
      "rgb(230,83,60)",
      "rgb(18,194,194)",
    ],
    // stroke: {
    //   width: [1.2],
    //   curve: ["smooth"],
    // },
    fill: {
      type: "gradient",
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.3,
        opacityTo: 0.6,
        stops: [0, 60, 100]
      },
      colors: [
        "rgb(132,90,223)",
        "rgb(35,183,229)",
        "rgb(245,184,73)",
        "rgb(73,182,245)",
        "rgb(38,191,148)",
        "rgb(230,83,60)",
        "rgb(18,194,194)",
      ],
    },
  },
};

const _Totalinvestments = {
  options: {
    chart: {
      height: 380,
      width: "100%",
      type: "area",
      animations: {
        initialAnimation: {
          enabled: false
        }
      } 
    },
    xaxis: {
      type: 'datetime'
    },
    fill: {
      type: "solid",
      opacity: 0.1,
      colors: [
        "rgb(  132,   90,  223  )",
        "rgb(   35,  183,  229  )",
        "rgb(  245,  184,   73  )",
        "rgb(   73,  182,  245  )",
        "rgb(   38,  191,  148  )",
        "rgb(  230,   83,   60  )",
        "rgb(   18,  194,  194  )",
      ],
    },
  },
  series: [
    {
      name: "Series 1",
      data: [
        [1486684800000, 34], 
        [1486771200000, 43], 
        [1486857600000, 31] , 
        [1486944000000, 43], 
        [1487030400000, 33], 
        [1487116800000, 52]
      ]
    },
    {
      name: "Series 2",
      data: [
        [1486684800000, 22], 
        [1486771200000, 53], 
        [1486857600000, 32] , 
        [1486944000000, 39], 
        [1487030400000, 41], 
        [1487116800000, 51]
      ]
    },
    {
      name: "Series 3",
      data: [
        [1486684800000, 43], 
        [1486771200000, 44], 
        [1486857600000, 51] , 
        [1486944000000, 46], 
        [1487030400000, 36], 
        [1487116800000, 30]
      ]
    },
    {
      name: "Series 4",
      data: [
        [1486684800000, 37], 
        [1486771200000, 27], 
        [1486857600000, 24] , 
        [1486944000000, 24], 
        [1487030400000, 36], 
        [1487116800000, 32]
      ]
    },
    {
      name: "Series 5",
      data: [
        [1486684800000, 45], 
        [1486771200000, 34], 
        [1486857600000, 20] , 
        [1486944000000, 33], 
        [1487030400000, 24], 
        [1487116800000, 32]
      ]
    }
  ],
};

export default function TotalInvestimentsWidget(props: ITotalInvestimentsWidgetProps) {
  const { className, ...restProps } = props;

  console.log(Totalinvestments);

  return (
    <>
      <div {...restProps} className={twMerge("TotalInvestimentsWidget box", className)}>
        <div className="box-header justify-between">
          <div className="box-title mb-2 sm:mb-0">Total Investments</div>
          <div className="inline-flex rounded-md shadow-sm" role="group" aria-label="Basic example">
            <button type="button" className="ti-btn-group !border-0 !text-xs !py-2 !px-3 ti-btn-primary-full !text-white">
              1D
            </button>
            <button type="button" className="ti-btn-group !border-0 !text-xs !py-2 !px-3 ti-btn-primary">
              1W
            </button>
            <button type="button" className="ti-btn-group !border-0 !text-xs !py-2 !px-3 ti-btn-primary">
              1M
            </button>
            <button type="button" className="ti-btn-group !border-0 !text-xs !py-2 !px-3 ti-btn-primary">
              3M
            </button>
            <button type="button" className="ti-btn-group !border-0 !text-xs !py-2 !px-3 ti-btn-primary">
              6M
            </button>
            <button type="button" className="ti-btn-group !border-0 !text-xs !py-2 !px-3 ti-btn-primary !rounded-s-none">
              1Y
            </button>
          </div>
        </div>
        <div className="box-body">
          <div className="flex items-center justify-between gap-4 flex-wrap">
            <div className="flex flex-wrap flex-grow gap-3 sm:ms-[3rem] ms-0">
              <div>
                <span className="block text-[#8c9097] dark:text-white/50 mb-1">Invested Value</span>
                <span className="block font-semibold">
                  $1,290.94
                  <span className="text-success ms-1 text-[0.75rem]">
                    <i className="ti ti-chevron-up"></i>1.22%
                  </span>
                </span>
              </div>
              <div>
                <span className="block text-[#8c9097] dark:text-white/50 mb-1">Total Returns</span>
                <span className="block font-semibold">
                  $25,458.20
                  <span className="text-success ms-1 text-[0.75rem]">
                    <i className="ti ti-chevron-up"></i>10.14%
                  </span>
                </span>
              </div>
            </div>
            <div className="flex flex-wrap gap-3 text-end">
              <div>
                <span className="block text-[#8c9097] dark:text-white/50 mb-1">Today Change</span>
                <span className="block font-semibold">$112.09</span>
              </div>
              <div>
                <span className="block text-[#8c9097] dark:text-white/50 mb-1">Change In %</span>
                <span className="block font-semibold">
                  +0.01%
                  <span className="text-success ms-1 text-[0.75rem]">
                    <i className="ti ti-chevron-up"></i>0.21%
                  </span>
                </span>
              </div>
            </div>
          </div>
          <div id="totalInvestmentsStats">
            <ReactApexChart
              options={Totalinvestments.options}
              series={Totalinvestments.series}
              type="area"
              width={"100%"}
              height={320}
            />
          </div>
        </div>
      </div>
    </>
  );
}
