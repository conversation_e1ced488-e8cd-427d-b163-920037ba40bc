"use client";
import { ApexOptions } from "apexcharts";

interface spark3 {
  options?: ApexOptions;
  width?: number;
  height?: string | number;
  series?: ApexOptions["series"];
  label?: XAxisAnnotations;
  color?: string | string[] | (string & string[]) | undefined;
  endingShape?: string;
  enabled?: boolean;
}
//Sales Overview

export const NegociationsPerformanceData: spark3 = {
  series: [
    {
      name: "Negociações com Interação",
      data: [44, 42, 57, 86, 58, 55, 70, 43, 23, 54, 77, 34],
    },
    {
      name: "Acordos",
      data: [74, 72, 87, 116, 88, 85, 100, 73, 53, 84, 107, 64],
    },
  ],
  options: {
    chart: {
      events: {
        mounted: (chart) => {
          chart.windowResizeHandler();
        },
      },
      stacked: true,
      type: "bar",
      height: 325,
    },
    grid: {
      borderColor: "#f5f4f4",
      strokeDashArray: 5,
    },
    colors: [
      "rgb(132, 90, 223)",
      "rgba(132, 90, 223, 0.6)",
      "rgba(132, 90, 223, 0.3)",
      "#ebeff5",
    ],
    plotOptions: {
      bar: {
        colors: {
          ranges: [
            {
              from: -100,
              to: -46,
              color: "#ebeff5",
            },
            {
              from: -45,
              to: 0,
              color: "#ebeff5",
            },
          ],
        },
        columnWidth: "20%",
      },
    },
    dataLabels: {
      enabled: false,
    },
    legend: {
      show: true,
      position: "top",
    },
    yaxis: {
      title: {
        text: "Negociações",
        style: {
          color: "#adb5be",
          fontSize: "14px",
          fontFamily: "Montserrat, sans-serif",
          fontWeight: 600,
          cssClass: "apexcharts-yaxis-label",
        },
      },
      labels: {
        formatter: function (y) {
          return y.toFixed(0) + "";
        },
      },
    },
    xaxis: {
      categories: [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ],
      axisBorder: {
        show: false,
        color: "rgba(119, 119, 142, 0.05)",
        offsetX: 0,
        offsetY: 0,
      },
      axisTicks: {
        show: false,
        borderType: "solid",
        color: "rgba(119, 119, 142, 0.05)",
        offsetX: 0,
        offsetY: 0,
      },
      labels: {
        rotate: -90,
      },
    },
  },
};

export const NegociationsInProgressList = [
  {
    id: 1,
    phoneNumber: "+55 11 99999-9999",
    portfolio: "Portfolio 1",
    status: "Unliked",
    lastInteraction: "25,Mar 2025",
    color: "success",
    bg: "success/10",
  },
  {
    id: 2,
    phoneNumber: "+55 41 96666-6666",
    portfolio: "Portfolio 2",
    status: "Em Andamento",
    lastInteraction: "21,Mar 2025",
    color: "danger",
    bg: "danger/10",
  },
  {
    id: 3,
    phoneNumber: "+55 51 95555-5555",
    portfolio: "Portfolio 3",
    status: "Em Andamento",
    lastInteraction: "21,Mar 2025",
    color: "success",
    bg: "success/10",
  },
  {
    id: 4,
    phoneNumber: "+55 61 94444-4444",
    portfolio: "Portfolio 4",
    status: "Unlinked",
    lastInteraction: "25,Mar 2025",
    color: "danger",
    bg: "danger/10",
  },
  {
    id: 5,
    phoneNumber: "+55 71 93333-3333",
    portfolio: "Portfolio 5",
    status: "Em Andamento",
    lastInteraction: "30,Mar 2025",
    color: "success",
    bg: "success/10",
  },
];
