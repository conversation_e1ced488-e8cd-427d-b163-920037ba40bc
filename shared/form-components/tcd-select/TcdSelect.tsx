"use client";

import { startCase } from "lodash";
import { PropsWithChildren } from "react";
import { twMerge } from "tailwind-merge";

export interface ITcdSelectOption extends Partial<PropsWithChildren<JSX.IntrinsicElements["option"]>> {
  label: string;
  value: string;
}

export interface ITcdSelectProps extends Omit<Partial<PropsWithChildren<JSX.IntrinsicElements["select"]>>, "name"> {
  inputClassName?: string;
  labelClassName?: string;
  name: string;
  label?: string;
  placeholder?: string;
  options: ITcdSelectOption[];
}

export default function TcdSelect(props: ITcdSelectProps) {
  const { className, label, id, name, placeholder, inputClassName, labelClassName, options, ...restProps } = props;
  const tcd_id = !!id ? id : name;
  const tcd_label = !!label ? label : startCase(name.replace(/id$/i, ""));

  return (
    <div className={twMerge("TcdSelectContainer", "", className)}>
      <label htmlFor={tcd_id} className={twMerge("TcdSelectLabel", "form-label", labelClassName)}>
        {tcd_label}
      </label>
      <select
        id={tcd_id}
        className={twMerge("TcdSelect", "form-control", "appearance-none select-bg-chevron pr-12", inputClassName)}
        name={name}
        {...restProps}
      >
        {placeholder && !props.value && !props.defaultValue && (
          <option disabled selected className="text-muted">
            {placeholder}
          </option>
        )}

        {options.map((option, optIdx) => {
          const { value, label, children, ...restOptionProps } = option;
          const content = children ? children : label;

          return (
            <option key={`${tcd_id}-${optIdx}`} value={value} {...restOptionProps}>
              {content}
            </option>
          );
        })}
      </select>
    </div>
  );
}
