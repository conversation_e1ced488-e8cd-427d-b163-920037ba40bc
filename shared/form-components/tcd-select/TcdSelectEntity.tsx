'use client';
import TcdSelect, { ITcdSelectProps } from './TcdSelect';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

export interface ITcdSelectEntityProps
  extends Omit<ITcdSelectProps, 'options'>,
    UseQueryOptions<any> {}

export default function TcdSelectEntity(props: ITcdSelectEntityProps) {
  const {
    enabled,
    initialData,
    placeholderData,
    queryHash,
    behavior,
    gcTime,
    networkMode,
    queryKeyHashFn,
    refetchInterval,
    persister,
    refetchOnMount,
    refetchOnReconnect,
    refetchOnWindowFocus,
    retry,
    retryDelay,
    select,
    retryOnMount,
    staleTime,
    structuralSharing,
    notifyOnChangeProps,
    throwOnError,
    meta,
    maxPages,
    initialDataUpdatedAt,
    refetchIntervalInBackground,
    queryKey,
    queryFn,
    ...restProps
  } = props;

  const providerQuery = useQuery({
    enabled,
    initialData,
    placeholderData,
    queryHash,
    behavior,
    gcTime,
    networkMode,
    queryKeyHashFn,
    refetchInterval,
    persister,
    refetchOnMount,
    refetchOnReconnect,
    refetchOnWindowFocus,
    retry,
    retryDelay,
    select,
    retryOnMount,
    staleTime,
    structuralSharing,
    notifyOnChangeProps,
    throwOnError,
    meta,
    maxPages,
    initialDataUpdatedAt,
    refetchIntervalInBackground,
    queryKey,
    queryFn,
  });

  let optionsData: any = providerQuery.data;
  if (optionsData?.data) optionsData = optionsData.data;
  if (!optionsData) optionsData = [];

  return <TcdSelect options={optionsData as any} {...restProps} />;
}
