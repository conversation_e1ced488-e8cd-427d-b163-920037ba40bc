import { twMerge } from "tailwind-merge";

type Props = {
  className?: string;
  type: string;
  id: string;
  name: string;
  label?: string;
  value: string;
  disabled?: boolean;
  placeholder: string;
  inputClassName?: string;
  labelClassName?: string;
  onChange: (e: any) => void;
  onInput?: (e: any) => void;
  onPaste?: (e: any) => void;
};

export default function TcdInput({
  className,
  label,
  id,
  name,
  type,
  value,
  disabled,
  placeholder,
  inputClassName,
  labelClassName,
  onChange,
  onInput,
  onPaste,
}: Props) {
  const tcd_id = !!id ? id : name;
  const tcd_placeholder = !!placeholder ? placeholder : name;
  const tcd_type = !!type ? type : "text";

  return (
    <div className={twMerge("TcdInputContainer", "", className)}>
      {label && (
        <label
          htmlFor={tcd_id}
          className={twMerge("TcdInputLabel", "form-label", labelClassName)}
        >
          {label}
        </label>
      )}
      <input
        id={tcd_id}
        className={twMerge("TcdInput", "form-control", inputClassName)}
        name={name}
        placeholder={tcd_placeholder}
        type={tcd_type}
        onChange={onChange}
        onInput={onInput}
        onPaste={onPaste}
        value={value}
        disabled={disabled}
      />
    </div>
  );
}
