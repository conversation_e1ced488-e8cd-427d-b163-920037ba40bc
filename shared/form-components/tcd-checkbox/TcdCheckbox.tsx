"use client";

import { startCase } from "lodash";
import { PropsWithChildren } from "react";
import { twMerge } from "tailwind-merge";

export interface ITcdCheckboxProps extends Omit<Partial<PropsWithChildren<JSX.IntrinsicElements["input"]>>, "name" | "type"> {
  inputClassName?: string;
  labelClassName?: string;
  name: string;
  label?: string;
  checkboxAfter?: boolean;
}

export default function TcdCheckbox(props: ITcdCheckboxProps) {
  const { className, label, id, name, placeholder, inputClassName, labelClassName, checkboxAfter, ...restProps } = props;
  const tcd_id = !!id ? id : name;
  const tcd_label = !!label ? label : startCase(name);
  const tcd_placeholder = !!placeholder ? placeholder : name;

  return (
    <div className={twMerge("TcdCheckboxContainer", "form-check form-check-md flex items-center cursor-pointer", className)}>
      {checkboxAfter && (
        <label htmlFor={tcd_id} className={twMerge("TcdCheckboxLabel", "form-check-label cursor-pointer pe-2", labelClassName)}>
          {tcd_label}
        </label>
      )}
      <input
        id={tcd_id}
        className={twMerge("TcdCheckbox", "form-check-input !bg-primary/10 checked:!bg-primary cursor-pointer", inputClassName)}
        name={name}
        placeholder={tcd_placeholder}
        type="checkbox"
        {...restProps}
      />
      {!checkboxAfter && (
        <label htmlFor={tcd_id} className={twMerge("TcdCheckboxLabel", "form-check-label cursor-pointer", labelClassName)}>
          {tcd_label}
        </label>
      )}
    </div>
  );
}