import getEnv from "../_utils/hooks/getEnv";
import { Fetch } from "@/_interceptors";
import { getAllPortfolios, Portfolio } from "./portfolio.provider";
import { getCustomer } from "./customer.provider";
import { getAccessToken } from "@/_interceptors/getJWTToken";

export type Paginated<T> = {
  items: T[];
  total: number;
  limit: number;
  page: number;
  totalPages: number;
};

export interface IPortfolioItem {
  id: string;
  portfolioId: string;
  portfolioName?: string;
  phoneNumber: string;
  customData?: Record<string, string>;
  contactName: string;
  customDataId: string;
  line: number;
  lastInteraction?: Date;
  waitingBusinessUserResponse?: boolean;
  currentStatus: PortfolioItemStatus;
  createdAt?: Date;
  updatedAt?: Date;
  insights?: string;
}

export enum PortfolioItemStatus {
  PENDING = "PENDING", // aguardando envio da primeira mensagem
  IN_PROGRESS = "IN_PROGRESS", //envio da primeira mensagem com sucesso
  PAUSED = "PAUSED", // parado pelo customer e pode voltar a in progress
  SUCCEED = "SUCCEED", // negociação finalizou com sucesso
  FAILED = "FAILED", // negociação finalizou sem sucesso
  CANCELLED = "CANCELLED", // item cancelado pelo customer por resolução externa
  UNLINKED = "UNLINKED", // O customer desvinculou a fala da IA e passa a digitar as mensagens por conta própria
  IDLE = "IDLE", // This status will be used when last_interaction is greater than portfolio.idle_after
  FOLLOWED_UP = "FOLLOWED_UP", //foi feito um envio de follow-up e está aguardando resposta
  SCHEDULED_FOLLOW_UP = "SCHEDULED_FOLLOW_UP", // acompanhamento agendado/follow-up agendado
  OPTED_OUT = "OPTED_OUT", // cliente optou por não continuar a conversa
  FINISHED = "FINISHED", //Atendimento encerrado após os follow-ups
}

export const portfolioItemStatusTranslation: { [key: string]: string } = {
  PENDING: "Em fila",
  IN_PROGRESS: "Em andamento",
  PAUSED: "Pausado",
  SUCCEED: "Concluído",
  FAILED: "Falhou",
  CANCELLED: "Cancelado",
  UNLINKED: "Operação Manual",
  IDLE: "Inativo",
  SCHEDULED_FOLLOW_UP: "Follow up agendado",
  FOLLOWED_UP: "Em follow-up",
  OPTED_OUT: "Não perturbe",
  FINISHED: "Atendimento encerrado",
};

export type PortfolioStats = {
  portfolioId: string;
  portfolioName: string;
  currentStatus: string;
  totalItems: number;
  stats: {
    current_status: string;
    total: number;
  }[];
};

export interface IMessageHistory {
  id: string;
  role: string;
  messageText: string;
  messageType: string;
  lang: string;
  fileUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  sent?: boolean;
  sent_at?: Date;
  time_to_go?: Date;
}

const transcendenceServiceEndpoint = getEnv("TRANSCENDENCE_SERVICE_URL");

export async function getAllPortfolioItems(
  searchParams: {
    currentStatus?: PortfolioItemStatus;
    portfolioId?: string;
    phoneNumber?: string;
    waitingBusinessUserResponse?: boolean;
    lastInteraction?: any;
  },
  pagination: { page?: number; limit?: number },
  portfolios: Portfolio[]
): Promise<Paginated<IPortfolioItem>> {
  try {
    const page = pagination.page || 1;
    const limit = pagination.limit || 10;

    const hasFilters = Object.keys(searchParams).length > 0;

    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const mockPaginatedItems = mockedPortfolioItems.slice(
        (page - 1) * limit,
        page * limit
      );
      const filteredItems = mockedPortfolioItems.filter((item) => {
        const matchesCurrentStatus = searchParams.currentStatus
          ? item.currentStatus === searchParams.currentStatus
          : true;
        const matchesPortfolioId = searchParams.portfolioId
          ? item.portfolioId === searchParams.portfolioId
          : true;
        const matchesPhoneNumber = searchParams.phoneNumber
          ? item.phoneNumber === searchParams.phoneNumber
          : true;

        return matchesCurrentStatus && matchesPortfolioId && matchesPhoneNumber;
      });

      const filteredPaginatedItems = filteredItems.slice(
        (page - 1) * limit,
        page * limit
      );

      let portfolioItems = hasFilters
        ? filteredPaginatedItems
        : mockPaginatedItems;
      portfolios =
        portfolios.length > 0 ? portfolios : await getAllPortfolios();

      portfolioItems = portfolioItems.map((item) => {
        return {
          ...item,
          portfolioName:
            portfolios.find((portfolio) => portfolio.id === item.portfolioId)
              ?.name || "",
        };
      });

      return Promise.resolve({
        items: portfolioItems,
        total: hasFilters ? filteredItems.length : mockedPortfolioItems.length,
        limit,
        page,
        totalPages: hasFilters
          ? filteredItems.length / limit
          : mockedPortfolioItems.length / limit,
      });
    }

    const customer = getCustomer();
    const customerId = customer.id;

    const api = `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/search/paginated/${customerId}?sort={"lastInteraction": "desc"}&page=${page}&limit=${limit}`;

    const fetch = Fetch();

    const data = await fetch.post(api, searchParams);

    const portfolioItems = data as Paginated<IPortfolioItem>;

    //TODO: Refactory to return the portfolio name in the API
    portfolios = portfolios.length > 0 ? portfolios : await getAllPortfolios();

    portfolioItems.items = portfolioItems.items.map((item) => {
      return {
        ...item,
        portfolioName:
          portfolios.find((portfolio) => portfolio.id === item.portfolioId)
            ?.name || "",
      };
    });

    return portfolioItems;
  } catch (error) {
    console.error("Error fetching portfolios", error);
    throw error;
  }
}

export async function getPortfolioItem(id: string): Promise<IPortfolioItem> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const item =
        mockedPortfolioItems.find((item) => item.id === id) ||
        mockedPortfolioItems[0];
      return Promise.resolve(item);
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}`
    );

    const middlewareResponses: Record<string, string> = {};

    const getFirstPropertyValue = (obj: any): any => {
      if (obj && typeof obj === "object") {
        const firstKey = Object.keys(obj)[0];
        return obj[firstKey];
      }
      return null;
    };

    if (
      data.middlewareResponseOutput &&
      typeof data.middlewareResponseOutput === "object"
    ) {
      Object.entries(data.middlewareResponseOutput).forEach(([key, value]) => {
        if (value && typeof value === "object") {
          middlewareResponses[key] = getFirstPropertyValue(value);
        }
      });
    }

    const insights = Object.values(middlewareResponses).join("\n");

    const portfolioItem = data as IPortfolioItem;
    portfolioItem.insights = insights;

    return portfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio item: " + id, error);
    throw error;
  }
}

export async function getConversationHistory(
  id: string
): Promise<IMessageHistory[]> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      return Promise.resolve(mockedConversationHistory);
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history`
    );

    return data as IMessageHistory[];
  } catch (error) {
    console.error("Error fetching portfolio item: " + id, error);
    throw error;
  }
}

export async function sendMessage(
  id: string,
  message: string,
  file?: File,
  messageType: string = "TEXT"
): Promise<IMessageHistory[]> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      return Promise.resolve(mockedConversationHistory);
    }

    const fetch = Fetch();
    const formData = new FormData();
    formData.append("message", message);
    formData.append("messageType", messageType);
    formData.append("roleType", "attendant");

    if (file) {
      formData.append("file", file);
    }

    const data = await fetch.post(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history`,
      formData
    );

    return data as IMessageHistory[];
  } catch (error) {
    console.error("Error sending message:", error);
    throw error;
  }
}

export async function downloadConversationFile(
  id: string,
  fileName: string
): Promise<Blob> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      // Return a mock Blob for testing purposes
      const mockBlob = new Blob(["Mock content"], { type: "text/plain" });
      return mockBlob;
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history/files/${fileName}`
    );

    const blob = new Blob([data]);
    return blob;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export async function downloadAudioFile(
  id: string,
  filePath: string
): Promise<Blob> {
  const encodedPath = encodeURIComponent(filePath);
  const token = await getAccessToken();

  const response = await fetch(
    `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${id}/conversation-history/files/audio/${encodedPath}`,
    {
      method: "GET",
      credentials: "include",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Erro ao baixar áudio: ${response.status}`);
  }
  const blob = await response.blob();

  console.log("DEBUG BLOB", blob.type, blob.size);

  return blob;
}

export async function setPortfolioItemStatusToInProgress(
  portfolioItemId: string
): Promise<IPortfolioItem> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const item =
        mockedPortfolioItems.find((item) => item.id === portfolioItemId) ||
        mockedPortfolioItems[0];

      return Promise.resolve({
        ...item,
        currentStatus: PortfolioItemStatus.IN_PROGRESS,
      });
    }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${portfolioItemId}/execute`
    );

    return data as IPortfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioItemStatusToPause(
  portfolioItemId: string
): Promise<IPortfolioItem> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const item =
        mockedPortfolioItems.find((item) => item.id === portfolioItemId) ||
        mockedPortfolioItems[0];

      return Promise.resolve({
        ...item,
        currentStatus: PortfolioItemStatus.PAUSED,
      });
    }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${portfolioItemId}/pause`
    );

    return data as IPortfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioItemStatusToCancel(
  portfolioItemId: string
): Promise<IPortfolioItem> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const item =
        mockedPortfolioItems.find((item) => item.id === portfolioItemId) ||
        mockedPortfolioItems[0];

      return Promise.resolve({
        ...item,
        currentStatus: PortfolioItemStatus.CANCELLED,
      });
    }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${portfolioItemId}/cancel`
    );

    return data as IPortfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioItemStatusToSucceed(
  portfolioItemId: string
): Promise<IPortfolioItem> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const item =
        mockedPortfolioItems.find((item) => item.id === portfolioItemId) ||
        mockedPortfolioItems[0];

      return Promise.resolve({
        ...item,
        currentStatus: PortfolioItemStatus.SUCCEED,
      });
    }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${portfolioItemId}/succeed`
    );

    return data as IPortfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioItemStatusToFinished(
  portfolioItemId: string
): Promise<IPortfolioItem> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const item =
        mockedPortfolioItems.find((item) => item.id === portfolioItemId) ||
        mockedPortfolioItems[0];

      return Promise.resolve({
        ...item,
        currentStatus: PortfolioItemStatus.FINISHED,
      });
    }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolio-items/${portfolioItemId}/finish`
    );

    return data as IPortfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

const mockedConversationHistory: IMessageHistory[] = [
  {
    role: "assistant",
    messageText:
      "Olá! Sua dívida de R$ 3.120,00 foi contraída em 20 de novembro de 2023 com o Nubank e está em atraso em nosso sistema de cobrança. Pode me falar sobre sua situação financeira atual e se há algo impactando sua capacidade de pagamento?",
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
    sent: true,
    sent_at: new Date(),
  },
  {
    role: "user",
    messageText: "Quero parcelar essa divida em 3 parcelas de R$ 500,00",
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "assistant",
    messageText: `Entendo que você gostaria de parcelar a dívida em valores menores. Agradeço por compartilhar sua proposta.
Atualmente, temos algumas opções disponíveis. A primeira, que gostaria de destacar, é a possibilidade de quitar a dívida à vista com um desconto. Você poderia efetuar o pagamento de R$ 2.720,00 de uma só vez.
Resolver essa dívida hoje pode te trazer mais tranquilidade e evitar problemas futuros, como restrições de crédito ou cobranças adicionais. Que tal considerar essa opção? Posso ajudar você a dar esse passo agora?`,
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
    sent: false,
    sent_at: undefined,
    time_to_go: new Date(new Date().getTime() + 1000 * 60 * 60 * 24),
  },
  {
    role: "user",
    messageText: "Não, quero pagar R$ 2.000,00",
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "assistant",
    messageText: `Entendo que você gostaria de pagar R$ 2.000,00 e respeitamos sua situação. 
Considerando isso, a melhor opção que temos é o parcelamento em 3 parcelas mensais. Este plano terá um pagamento mensal de aproximadamente R$ 1.106,60, totalizando R$ 3.319,90. Essa alternativa evita uma maior carga no seu orçamento mensal, além de oferecer um prazo mais curto para quitação da dívida.
Resolver a dívida hoje pode te trazer mais tranquilidade e evitar problemas futuros. Que tal considerarmos essa opção? Posso ajudar você a dar esse passo agora?`,
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "user",
    messageText: "Não, esse valor é ruim.",
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "assistant",
    messageText: `Entendo a sua preocupação e agradeço por compartilhar. Vamos trabalhar juntos para encontrar uma solução que se ajuste à sua situação.
Qual seria o valor máximo que você poderia pagar à vista ou mensalmente? Isso me ajudará a apresentar uma proposta que seja viável para você.`,
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "user",
    messageText: "Quero pagar duas parcelas de mil reais",
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "assistant",
    messageText: `Agradeço por compartilhar sua proposta de pagar duas parcelas de R$ 1.000,00. No entanto, nossa opção de parcelamento mais próxima seria a de 3 parcelas mensais com um valor de R$ 1.106,60, totalizando R$ 3.319,90.
Resolver essa dívida hoje pode te trazer mais tranquilidade e evitar problemas futuros, como restrições de crédito ou cobranças adicionais. Que tal considerarmos a opção de 3 parcelas? Posso ajudar você a dar esse passo agora?`,
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "user",
    messageText: "Fechado então",
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "assistant",
    messageText: `Fico feliz que tenha gostado, Carlos! Então, vamos formalizar a proposta de pagamento em 3 parcelas mensais de R$ 1.106,60, totalizando R$ 3.319,90.
Posso te encaminhar para o setor de pagamentos para combinar os detalhes da transação. Você confirma o aceite dessa proposta?`,
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "user",
    messageText: "blz",
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
  {
    role: "assistant",
    messageText: `Parabéns e obrigada pela confiança, Carlos! Estou te encaminhando para o setor de pagamentos para combinar os detalhes da transação.
Se precisar de mais alguma coisa, estarei à disposição. Tenha um ótimo dia!`,
    messageType: "text",
    lang: "pt-BR",
    createdAt: new Date(),
    updatedAt: new Date(),
    id: "",
  },
];

const mockedPortfolioItems: IPortfolioItem[] = [
  {
    id: "2837e3a4-b015-4f27-a9f5-d9a664b5bfb1",
    contactName: "Mercedes Cronin DDS",
    phoneNumber: "5528975052791",
    currentStatus: PortfolioItemStatus.SUCCEED,
    lastInteraction: new Date("2024-11-05T17:53:32.509Z"),
    createdAt: new Date("2024-10-24T19:34:49.803Z"),
    updatedAt: new Date("2024-10-22T08:16:18.708Z"),
    portfolioId: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "49ad15d5-83d1-468e-8b46-e5a764b0bbed",
    contactName: "Tabitha Treutel",
    phoneNumber: "5549936591809",
    currentStatus: PortfolioItemStatus.SUCCEED,
    lastInteraction: new Date("2024-11-06T03:21:46.547Z"),
    createdAt: new Date("2024-10-29T04:17:19.130Z"),
    updatedAt: new Date("2024-10-29T22:31:58.628Z"),
    portfolioId: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "f0a7b402-59c4-40d5-82e6-bcbf6ddbdc1c",
    contactName: "Mr. Terrance Fisher",
    phoneNumber: "5520904540564",
    currentStatus: PortfolioItemStatus.PENDING,
    lastInteraction: new Date("2024-11-05T14:22:06.303Z"),
    createdAt: new Date("2024-09-10T14:28:54.034Z"),
    updatedAt: new Date("2024-10-21T13:04:26.107Z"),
    portfolioId: "1709c2a6-f91e-4568-9699-a8f211d1c1a5",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "a1ab2827-aba5-4cb1-8b31-1ecac30652c6",
    contactName: "James Cummings",
    phoneNumber: "5567993202172",
    currentStatus: PortfolioItemStatus.PENDING,
    lastInteraction: new Date("2024-11-06T03:54:32.201Z"),
    createdAt: new Date("2024-10-12T00:11:24.768Z"),
    updatedAt: new Date("2024-11-02T08:11:19.044Z"),
    portfolioId: "1709c2a6-f91e-4568-9699-a8f211d1c1a5",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "cb6df5fc-057d-43e2-b203-b947c4ff6543",
    contactName: "Charlie Bayer",
    phoneNumber: "5511922007067",
    currentStatus: PortfolioItemStatus.CANCELLED,
    lastInteraction: new Date("2024-11-05T14:25:35.570Z"),
    createdAt: new Date("2024-10-06T09:36:48.219Z"),
    updatedAt: new Date("2024-10-25T01:21:11.412Z"),
    portfolioId: "52d7eef0-1c29-4d4e-a738-03f80847313b",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "7957cc63-b1cc-4198-9f30-9ae602510fd7",
    contactName: "Krista Prohaska",
    phoneNumber: "5529962066410",
    currentStatus: PortfolioItemStatus.CANCELLED,
    lastInteraction: new Date("2024-11-05T15:10:12.965Z"),
    createdAt: new Date("2024-11-03T02:50:38.888Z"),
    updatedAt: new Date("2024-10-24T18:22:42.949Z"),
    portfolioId: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "8554450d-7a90-434d-9b8d-92d63dd50e61",
    contactName: "Arnold Beer",
    phoneNumber: "5517953662371",
    currentStatus: PortfolioItemStatus.IDLE,
    lastInteraction: new Date("2024-11-05T16:08:07.156Z"),
    createdAt: new Date("2024-10-25T02:48:45.153Z"),
    updatedAt: new Date("2024-10-22T05:19:04.338Z"),
    portfolioId: "52d7eef0-1c29-4d4e-a738-03f80847313b",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "d5b22b0b-1b96-43fd-abe0-26f35bac5d66",
    contactName: "Kelly Berge",
    phoneNumber: "5540996964688",
    currentStatus: PortfolioItemStatus.PAUSED,
    lastInteraction: new Date("2024-11-05T21:29:41.184Z"),
    createdAt: new Date("2024-09-14T03:41:00.368Z"),
    updatedAt: new Date("2024-10-23T06:28:57.539Z"),
    portfolioId: "d6613025-b00e-4734-86d2-8faef88c8c57",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "2f5f86a8-4a4d-4887-ad2a-bce3e5be2c1b",
    contactName: "Carla Davis",
    phoneNumber: "5569914907175",
    currentStatus: PortfolioItemStatus.IN_PROGRESS,
    lastInteraction: new Date("2024-11-05T08:08:19.749Z"),
    createdAt: new Date("2024-10-10T09:26:35.319Z"),
    updatedAt: new Date("2024-10-31T11:26:47.211Z"),
    portfolioId: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "5b4b97f6-5d29-4cc6-b68b-fb8addd4f714",
    contactName: "Samuel Beier",
    phoneNumber: "5543995688055",
    currentStatus: PortfolioItemStatus.CANCELLED,
    lastInteraction: new Date("2024-11-06T02:48:47.522Z"),
    createdAt: new Date("2024-10-07T08:40:00.074Z"),
    updatedAt: new Date("2024-10-25T13:00:47.609Z"),
    portfolioId: "52d7eef0-1c29-4d4e-a738-03f80847313b",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "c4e3b094-49bc-41a8-8c43-dd5eead03eed",
    contactName: "Hugo Tremblay DVM",
    phoneNumber: "5528902869203",
    currentStatus: PortfolioItemStatus.PENDING,
    lastInteraction: new Date("2024-11-05T12:58:50.941Z"),
    createdAt: new Date("2024-10-26T15:20:20.501Z"),
    updatedAt: new Date("2024-11-01T18:47:47.891Z"),
    portfolioId: "d6613025-b00e-4734-86d2-8faef88c8c57",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "61fdb8c9-8d1a-4993-9dce-4b43d48714d5",
    contactName: "Corey Funk",
    phoneNumber: "5553916972900",
    currentStatus: PortfolioItemStatus.IDLE,
    lastInteraction: new Date("2024-11-06T00:55:44.545Z"),
    createdAt: new Date("2024-10-10T01:42:11.747Z"),
    updatedAt: new Date("2024-10-20T17:48:16.429Z"),
    portfolioId: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "e456d938-425f-434c-a3d6-a6d5367744ed",
    contactName: "Maurice Huel",
    phoneNumber: "5589957599233",
    currentStatus: PortfolioItemStatus.FAILED,
    lastInteraction: new Date("2024-11-06T03:32:06.103Z"),
    createdAt: new Date("2024-10-21T10:33:10.931Z"),
    updatedAt: new Date("2024-10-20T09:13:43.583Z"),
    portfolioId: "8ba8aea5-0d82-48ed-a92a-a69fbda8abcd",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "16f2ae45-b1b9-4962-a41d-21beedc6b2de",
    contactName: "Josefina Koepp IV",
    phoneNumber: "5597993751981",
    currentStatus: PortfolioItemStatus.IN_PROGRESS,
    lastInteraction: new Date("2024-11-05T11:47:13.804Z"),
    createdAt: new Date("2024-09-24T00:42:18.319Z"),
    updatedAt: new Date("2024-10-28T08:04:28.951Z"),
    portfolioId: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "f73f6e7f-94ab-498e-8e72-428e75dfb1c1",
    contactName: "Mr. Jim Borer PhD",
    phoneNumber: "5561944029610",
    currentStatus: PortfolioItemStatus.FAILED,
    lastInteraction: new Date("2024-11-05T11:53:16.105Z"),
    createdAt: new Date("2024-10-14T00:40:51.234Z"),
    updatedAt: new Date("2024-10-25T20:51:55.673Z"),
    portfolioId: "1709c2a6-f91e-4568-9699-a8f211d1c1a5",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "53e51edf-9f50-49a8-95e6-4ac4d7110e9a",
    contactName: "Dawn Bednar",
    phoneNumber: "5556998338494",
    currentStatus: PortfolioItemStatus.PENDING,
    lastInteraction: new Date("2024-11-05T05:32:28.819Z"),
    createdAt: new Date("2024-09-11T02:47:02.092Z"),
    updatedAt: new Date("2024-10-28T04:07:41.692Z"),
    portfolioId: "8ba8aea5-0d82-48ed-a92a-a69fbda8abcd",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "364a733a-3213-4680-8942-69b2d7a4222a",
    contactName: "Ron MacGyver",
    phoneNumber: "5529953200641",
    currentStatus: PortfolioItemStatus.SUCCEED,
    lastInteraction: new Date("2024-11-05T20:06:50.421Z"),
    createdAt: new Date("2024-10-21T22:06:23.960Z"),
    updatedAt: new Date("2024-11-02T06:05:37.026Z"),
    portfolioId: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "926b0a84-9c79-48f2-adb8-a1f42f8e20b3",
    contactName: "Clarence Hartmann",
    phoneNumber: "5533965861108",
    currentStatus: PortfolioItemStatus.IDLE,
    lastInteraction: new Date("2024-11-05T18:06:07.456Z"),
    createdAt: new Date("2024-09-23T18:25:26.914Z"),
    updatedAt: new Date("2024-10-18T17:42:42.614Z"),
    portfolioId: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "ea6dfae7-c5f0-4b3c-baa1-6a9952a989fa",
    contactName: "Alfredo Mosciski",
    phoneNumber: "5589917751005",
    currentStatus: PortfolioItemStatus.SUCCEED,
    lastInteraction: new Date("2024-11-05T20:29:31.656Z"),
    createdAt: new Date("2024-10-07T13:30:37.957Z"),
    updatedAt: new Date("2024-10-19T01:34:04.050Z"),
    portfolioId: "d6613025-b00e-4734-86d2-8faef88c8c57",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
  {
    id: "ba4bf125-6f2e-4bea-aee0-d584a58f4e51",
    contactName: "Mathew Stoltenberg IV",
    phoneNumber: "5586972411572",
    currentStatus: PortfolioItemStatus.IN_PROGRESS,
    lastInteraction: new Date("2024-11-05T10:04:16.115Z"),
    createdAt: new Date("2024-10-16T00:55:55.798Z"),
    updatedAt: new Date("2024-10-24T05:03:14.943Z"),
    portfolioId: "52d7eef0-1c29-4d4e-a738-03f80847313b",
    customDataId: "0b7a5d20-aa66-4e0b-9832-88c874181476",
    line: 3,
  },
];
