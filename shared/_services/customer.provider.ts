"use client";
import getEnv from "../_utils/hooks/getEnv";
import { Fetch } from "@/_interceptors";

export interface Customer {
  id: string;
  cnpj?: string;
  email?: string;
  name?: string;
  segment?: string; // fideleasy, collectcash, saleszap etc
  phone?: string;
  whatsappPhone?: string;
  userId: string;
  accountId: string;
  // status: string; // deleted flag
}

export function getCustomer(): Customer {
  if (getEnv("SERVICE_MOCK_RESPONSE")) {
    return mockedCustomer;
  }

  const storedData = localStorage.getItem("customer");
  const customerRaw = storedData ? JSON.parse(storedData) : {};

  const customer: Customer = {
    id: customerRaw.customerId,
    userId: customerRaw.userId,
    cnpj: customerRaw.cnpj,
    email: customerRaw.email,
    name: customerRaw.firstname + " " + customerRaw.lastname,
    segment: customerRaw.segment,
    phone: customerRaw.phone,
    whatsappPhone: customerRaw.whatsappPhone,
    accountId: customerRaw.accountId,

  };
  return customer;
}

const mockedCustomer: Customer = {
  id: getEnv("FORCE_CUSTOMER_ID"),
  cnpj: "**************",
  email: "<EMAIL>",
  name: "Demo Account",
  segment: "collectcash",
  phone: "*************",
  whatsappPhone: "*************",
  userId: '346b80ae-a70f-41cd-9d0a-854d3de5fd32',
  accountId: '346b80ae-a70f-41cd-9d0a-854d3de5fd62',
};

// CSV delimiter configuration constant (fallback default)
export const CSV_DELIMITER = ",";

// TypeScript interfaces for customer preferences API response
interface CustomerPreferencesResponse {
    portfolio?: {
      customImportConfig?: {
        delimiter?: string;
      };
  };
}

// Function to fetch customer preferences and extract CSV delimiter
export async function fetchCustomerCSVDelimiter(customerId: string): Promise<string> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      // Return default delimiter in mock mode
      return CSV_DELIMITER;
    }

    const fetch = Fetch();
    const transcendenceServiceEndpoint = getEnv('TRANSCENDENCE_SERVICE_URL') || 'http://localhost:3000/api';

    const response: CustomerPreferencesResponse = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/customer-preferences/${customerId}`
    );

    // Extract delimiter from the nested response structure
    const delimiter = response?.portfolio?.customImportConfig?.delimiter;

    // Return the delimiter if found, otherwise fall back to default
    return delimiter || CSV_DELIMITER;
  } catch (error) {
    console.warn('Failed to fetch customer CSV delimiter preferences:', error);
    // Fall back to default delimiter on any error
    return CSV_DELIMITER;
  }
};

