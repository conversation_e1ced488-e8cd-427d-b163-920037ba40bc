"use client";
import getEnv from "../_utils/hooks/getEnv";

export interface Customer {
  id: string;
  cnpj?: string;
  email?: string;
  name?: string;
  segment?: string; // fideleasy, collectcash, saleszap etc
  phone?: string;
  whatsappPhone?: string;
  userId: string;
  accountId: string;
  // status: string; // deleted flag
}

export function getCustomer(): Customer {
  if (getEnv("SERVICE_MOCK_RESPONSE")) {
    return mockedCustomer;
  }

  const storedData = localStorage.getItem("customer") ;
  const customerRaw = storedData ? JSON.parse(storedData) : {};

  const customer: Customer = {
    id: customerRaw.customerId,
    userId: customerRaw.userId,
    cnpj: customerRaw.cnpj,
    email: customerRaw.email,
    name: customerRaw.firstname + " " + customerRaw.lastname,
    segment: customerRaw.segment,
    phone: customerRaw.phone,
    whatsappPhone: customerRaw.whatsappPhone,
    accountId: customerRaw.accountId,

  };
  return customer;
}

const mockedCustomer: Customer = {
  id: getEnv("FORCE_CUSTOMER_ID"),
  cnpj: "**************",
  email: "<EMAIL>",
  name: "Demo Account",
  segment: "collectcash",
  phone: "*************",
  whatsappPhone: "*************",
  userId: '346b80ae-a70f-41cd-9d0a-854d3de5fd32',
  accountId: '346b80ae-a70f-41cd-9d0a-854d3de5fd62',
};
