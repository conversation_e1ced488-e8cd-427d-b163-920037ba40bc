import getEnv from "../_utils/hooks/getEnv";
import { Fetch } from "@/_interceptors";
import { PortfolioItemStatus } from "./portfolio-item.provider";

const transcendenceServiceEndpoint = getEnv("TRANSCENDENCE_SERVICE_URL");

export enum RecordStatus {
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export enum PortfolioImportStatus {
  UPLOADED = "UPLOADED",
  PROCESSING = "PROCESSING",
  SUCCESS = "SUCCESS",
  UNKNOWN = "UNKNOWN",
}

export enum PortfolioExecutionStatus {
  EXECUTING = "EXECUTING",
  QUEUED = "QUEUED",
  PAUSED = "PAUSED",
  WAITING = "WAITING",
  CANCELLED = "CANCELLED",
  INBOUND = "INBOUND_EXECUTING",
  FINISHED = "FINISHED",
}

export type PortfolioPerformance = {
  portfolioId: string;
  portfolioName: string;
  executionStatus: PortfolioExecutionStatus;
  totalItems: number;
  stats: Record<PortfolioItemStatus, number>;
};

export type Portfolio = {
  id: string;
  name: string;
  workflowId: string;
  workExpression: string;
  executeImmediately: boolean;
  customerId: string;
  executionStatus: PortfolioExecutionStatus;
  importStatus: PortfolioImportStatus;
  originalFileName: string;
  totalQuantity: number;
  processedQuantity: number;
  totalSuccessQuantity: number;
  totalFailedQuantity: number;
  importFinishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  status: RecordStatus;
  fileUrl: string;
};

export interface IPortfolioUpload {
  file: File;
  name: string;
  workflowId: string;
  executeImmediately?: boolean;
  communicationChannel: string;
}

export interface IPortfolioItem {
  id: string;
  phoneNumber: string;
  portfolioName: string;
  lastInteraction: string;
  currentStatus: string;
}

export async function createPortfolio(
  portfolioToUpload: IPortfolioUpload
): Promise<any> {
  const formData = new FormData();
  formData.append("file", portfolioToUpload.file, portfolioToUpload.file.name);
  formData.append("name", portfolioToUpload.name);
  formData.append("workflowId", portfolioToUpload.workflowId);
  formData.append("workExpression", "* 8-20 * * 1-5");
  formData.append(
    "executeImmediately",
    String(portfolioToUpload.executeImmediately)
  );
  formData.append(
    "communicationChannel",
    portfolioToUpload.communicationChannel
  );

  if (getEnv("SERVICE_MOCK_RESPONSE")) {
    return Promise.resolve(mockedPortfolios[0]);
  }

  const fetch = Fetch();
  const data = await fetch.post(
    `${transcendenceServiceEndpoint}/v1/business-base/portfolios/import`,
    formData
  );

  return data as Portfolio;
}

export async function getPortfolio(id: string): Promise<Portfolio> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      return Promise.resolve(mockedPortfolios[0]);
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${id}`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export async function fetchPortfolioMetrics(
  startDate: string,
  endDate: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/created?startDate=${startDate}&endDate=${endDate}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching portfolio metrics:", error);
    throw error;
  }
}

export async function fetchPortfolioItemsMetrics(
  startDate: string,
  endDate: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/created?startDate=${startDate}&endDate=${endDate}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching portfolio items metrics:", error);
    throw error;
  }
}

export async function fetchMessagesSentMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/messages-sent?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching messages sent metrics:", error);
    throw error;
  }
}

export async function fetchMessagesReceivedMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/messages-received?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching messages received metrics:", error);
    throw error;
  }
}

export async function fetchFirstMessagesSentMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/first-messages-sent?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching first messages sent metrics:", error);
    throw error;
  }
}

export async function fetchAnswerMessagesSentMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/answer-messages-sent?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching answer messages sent metrics:", error);
    throw error;
  }
}

export async function fetchPortfolioItemsOnlyAIMetrics(
  startDate: string,
  endDate: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/ai-only-interaction?startDate=${startDate}&endDate=${endDate}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching portfolio items with IA 100 metrics:", error);
    throw error;
  }
}

export async function fetchPortfolioItemsWithInteractionMetrics(
  startDate: string,
  endDate: string,
  groupByDate: string = "DAY"
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/with-interaction?startDate=${startDate}&endDate=${endDate}&groupByDate=${groupByDate}`
    );

    return data;
  } catch (error) {
    console.error(
      "Error fetching portfolio items with interaction metrics:",
      error
    );
    throw error;
  }
}

export async function fetchPortfolioItemsGroupedByDateMetrics(
  startDate: string,
  endDate: string,
  groupByDate: string = "DAY",
  currentStatus: string = "SUCCEED"
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/grouped-by-date?startDate=${startDate}&endDate=${endDate}&groupByDate=${groupByDate}&currentStatus=${currentStatus}`
    );

    return data;
  } catch (error) {
    console.error(
      "Error fetching portfolio items grouped by date metrics:",
      error
    );
    throw error;
  }
}

export async function getAllPortfolios(
  currentPage?: number
): Promise<Portfolio[]> {
  const page = currentPage || 1;
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      return Promise.resolve(mockedPortfolios);
    }

    const fetch = Fetch();
    // pagination still not implemented on the backend
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios?page=${page}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching portfolios", error);
    throw error;
  }
}

export async function getAllPortfolioPerformance(): Promise<
  PortfolioPerformance[]
> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      return Promise.resolve(mockedPortfolioPerformance);
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/performance/all`
    );

    return data as PortfolioPerformance[];
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function getPortfolioPerformance(
  portfolioId: string
): Promise<PortfolioPerformance> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      return Promise.resolve(mockedPortfolioPerformance[0]);
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}/performance`
    );

    return data as PortfolioPerformance;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioStatusToExecuting(
  portfolioId: string
): Promise<Portfolio> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const portfolioMock = mockedPortfolios.filter(
        (portfolio) => portfolio.id === portfolioId
      )[0];

      return {
        ...portfolioMock,
        executionStatus: PortfolioExecutionStatus.EXECUTING,
      };
    }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}/execute`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioStatusToPaused(
  portfolioId: string
): Promise<Portfolio> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const portfolioMock = mockedPortfolios.filter(
        (portfolio) => portfolio.id === portfolioId
      )[0];

      return {
        ...portfolioMock,
        executionStatus: PortfolioExecutionStatus.PAUSED,
      };
    }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}/pause`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioStatusToCancelled(
  portfolioId: string
): Promise<Portfolio> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const portfolioMock = mockedPortfolios.filter(
        (portfolio) => portfolio.id === portfolioId
      )[0];

      return {
        ...portfolioMock,
        executionStatus: PortfolioExecutionStatus.CANCELLED,
      };
    }

    const fetch = Fetch();
    const data = await fetch.put(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}/cancel`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function setPortfolioStatusToDeleted(
  portfolioId: string
): Promise<Portfolio> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      const portfolioMock = mockedPortfolios.filter(
        (portfolio) => portfolio.id === portfolioId
      )[0];

      return {
        ...portfolioMock,
        status: RecordStatus.DELETED,
      };
    }

    const fetch = Fetch();
    const data = await fetch.delete(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${portfolioId}`
    );

    return data as Portfolio;
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    throw error;
  }
}

export async function downloadOriginalFile(id: string): Promise<Blob> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      // Return a mock Blob for testing purposes
      const mockBlob = new Blob(["Mock content"], { type: "text/plain" });
      return mockBlob;
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${id}/download`
    );

    const blob = new Blob([data]);
    return blob;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export async function downloadImportErrorsFile(id: string): Promise<Blob> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      // Return a mock Blob for testing purposes
      const mockBlob = new Blob(["Mock content"], { type: "text/plain" });
      return mockBlob;
    }

    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${id}/download/import-errors`
    );

    const blob = new Blob([data]);
    return blob;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export async function exportPortfolioData(id: string): Promise<Blob> {
  try {
    if (getEnv("SERVICE_MOCK_RESPONSE")) {
      // Return a mock Blob for testing purposes
      const mockBlob = new Blob(["Mock content"], { type: "text/plain" });
      return mockBlob;
    }

    const fetch = Fetch();
    const data = await fetch.post(
      `${transcendenceServiceEndpoint}/v1/business-base/portfolios/${id}/export`,
      {}
    );

    const blob = new Blob([data]);
    return blob;
  } catch (error) {
    console.error("Error fetching portfolio: " + id, error);
    throw error;
  }
}

export const mockedPortfolios: Portfolio[] = [
  {
    id: "8ba8aea5-0d82-48ed-a92a-a69fbda8abcd",
    customerId: "346b80ae-a77f-41cd-9d0a-854d3de5fd31",
    workflowId: "c23e76d1-7713-4325-91ff-4bd660d4d2f0",
    name: "Fatura Cartão Atrasada 2020",
    originalFileName: "abscond_gah.csv",
    fileUrl:
      "http://collectcash.s3.localhost.localstack.cloud:4566/346b80ae-a77f-41cd-9d0a-854d3de5fd31/8ba8aea5-0d82-48ed-a92a-a69fbda8abcd.csv",
    importStatus: PortfolioImportStatus.UPLOADED,
    totalQuantity: 14993,
    executionStatus: PortfolioExecutionStatus.EXECUTING,
    executeImmediately: false,
    workExpression: "* * * * *",
    createdAt: new Date("2024-11-05T23:45:11.621Z"),
    updatedAt: new Date("2024-11-05T23:46:51.804Z"),
    processedQuantity: 0,
    totalSuccessQuantity: 0,
    totalFailedQuantity: 0,
    status: RecordStatus.ACTIVE,
  },
  {
    id: "52d7eef0-1c29-4d4e-a738-03f80847313b",
    customerId: "346b80ae-a77f-41cd-9d0a-854d3de5fd31",
    workflowId: "43e3ceed-58da-4ff2-bddf-67cb79d4433f",
    name: "Precatórios 2000",
    originalFileName: "though_consequently_unlike.csv",
    fileUrl:
      "http://collectcash.s3.localhost.localstack.cloud:4566/346b80ae-a77f-41cd-9d0a-854d3de5fd31/52d7eef0-1c29-4d4e-a738-03f80847313b.csv",
    importStatus: PortfolioImportStatus.PROCESSING,
    totalQuantity: 28123,
    processedQuantity: 14061,
    totalSuccessQuantity: 7020,
    totalFailedQuantity: 7041,
    executionStatus: PortfolioExecutionStatus.QUEUED,
    executeImmediately: true,
    workExpression: "* * * * *",
    createdAt: new Date("2024-11-05T23:45:11.621Z"),
    updatedAt: new Date("2024-11-05T23:46:50.006Z"),
    status: RecordStatus.ACTIVE,
  },
  {
    id: "d6613025-b00e-4734-86d2-8faef88c8c57",
    customerId: "346b80ae-a77f-41cd-9d0a-854d3de5fd31",
    workflowId: "43e3ceed-58da-4ff2-bddf-67cb79d4433f",
    name: "Empréstimo PJ - Inter",
    originalFileName: "hamster-buckridge-llc.csv",
    fileUrl:
      "http://collectcash.s3.localhost.localstack.cloud:4566/346b80ae-a77f-41cd-9d0a-854d3de5fd31/d6613025-b00e-4734-86d2-8faef88c8c57.csv",
    importStatus: PortfolioImportStatus.PROCESSING,
    importFinishedAt: new Date("2024-11-05T23:33:10.004Z"),
    totalQuantity: 14690,
    processedQuantity: 12345,
    totalSuccessQuantity: 10000,
    totalFailedQuantity: 2345,
    executionStatus: PortfolioExecutionStatus.CANCELLED,
    executeImmediately: true,
    workExpression: "* * * * *",
    createdAt: new Date("2024-11-05T23:32:40.636Z"),
    updatedAt: new Date("2024-11-05T23:45:06.677Z"),
    status: RecordStatus.ACTIVE,
  },
  {
    id: "1709c2a6-f91e-4568-9699-a8f211d1c1a5",
    customerId: "346b80ae-a77f-41cd-9d0a-854d3de5fd31",
    workflowId: "32b4076e-fad4-4fe8-a076-8854a628f5ad",
    name: "Empréstimo Pessoal Itaú",
    originalFileName: "bowed_velvety.csv",
    fileUrl:
      "http://collectcash.s3.localhost.localstack.cloud:4566/346b80ae-a77f-41cd-9d0a-854d3de5fd31/1709c2a6-f91e-4568-9699-a8f211d1c1a5.csv",
    importStatus: PortfolioImportStatus.SUCCESS,
    totalQuantity: 30000,
    processedQuantity: 30000,
    totalSuccessQuantity: 20000,
    totalFailedQuantity: 10000,
    executionStatus: PortfolioExecutionStatus.EXECUTING,
    executeImmediately: true,
    workExpression: "* * * * *",
    createdAt: new Date("2024-11-05T23:46:28.590Z"),
    updatedAt: new Date("2024-11-05T23:46:50.006Z"),
    status: RecordStatus.ACTIVE,
  },
  {
    id: "30b0e1fb-64c7-4519-93aa-4eb5dd33087e",
    customerId: "346b80ae-a77f-41cd-9d0a-854d3de5fd31",
    workflowId: "32b4076e-fad4-4fe8-a076-8854a628f5ad",
    name: "Fianciamento Veículo - BV",
    originalFileName: "monkey-conn-group.csv",
    fileUrl:
      "http://collectcash.s3.localhost.localstack.cloud:4566/346b80ae-a77f-41cd-9d0a-854d3de5fd31/30b0e1fb-64c7-4519-93aa-4eb5dd33087e.csv",
    importStatus: PortfolioImportStatus.SUCCESS,
    importFinishedAt: new Date("2024-11-05T23:33:10.004Z"),
    totalQuantity: 30880,
    processedQuantity: 30880,
    totalSuccessQuantity: 20255,
    totalFailedQuantity: 4815,
    executionStatus: PortfolioExecutionStatus.EXECUTING,
    executeImmediately: true,
    workExpression: "* * * * *",
    createdAt: new Date("2024-11-05T23:32:40.636Z"),
    updatedAt: new Date("2024-11-05T23:45:06.677Z"),
    status: RecordStatus.ACTIVE,
  },
  {
    id: "8e27e190-1db7-4b73-8436-2611ca9a5d11",
    customerId: "346b80ae-a77f-41cd-9d0a-854d3de5fd31",
    workflowId: "43e3ceed-58da-4ff2-bddf-67cb79d4433f",
    name: "Cheque Especial - Santander",
    originalFileName: "peacock-welch-llc.csv",
    fileUrl:
      "http://collectcash.s3.localhost.localstack.cloud:4566/346b80ae-a77f-41cd-9d0a-854d3de5fd31/8e27e190-1db7-4b73-8436-2611ca9a5d11.csv",
    importStatus: PortfolioImportStatus.SUCCESS,
    importFinishedAt: new Date("2024-11-05T23:33:10.004Z"),
    totalQuantity: 11864,
    processedQuantity: 11864,
    totalSuccessQuantity: 9714,
    totalFailedQuantity: 2150,
    executionStatus: PortfolioExecutionStatus.QUEUED,
    executeImmediately: true,
    workExpression: "* * * * *",
    createdAt: new Date("2024-11-05T23:32:40.636Z"),
    updatedAt: new Date("2024-11-05T23:45:06.677Z"),
    status: RecordStatus.ACTIVE,
  },
];

const mockedPortfolioPerformance: PortfolioPerformance[] = [
  {
    portfolioId: "8ba8aea5-0d82-48ed-a92a-a69fbda8abcd",
    portfolioName: "Portfólio 1",
    executionStatus: PortfolioExecutionStatus.EXECUTING,
    totalItems: 500,
    stats: {
      IN_PROGRESS: 80,
      SUCCEED: 20,
      FAILED: 30,
      CANCELLED: 40,
      UNLINKED: 50,
      IDLE: 5,
      PENDING: 170,
      SCHEDULED_FOLLOW_UP: 0,
      PAUSED: 80,
      FOLLOWED_UP: 0,
      OPTED_OUT: 0,
      FINISHED: 0,
    },
  },
  {
    portfolioId: "2",
    portfolioName: "Portfólio 2",
    executionStatus: PortfolioExecutionStatus.EXECUTING,
    totalItems: 1000,
    stats: {
      IN_PROGRESS: 100,
      SUCCEED: 200,
      FAILED: 300,
      CANCELLED: 400,
      UNLINKED: 500,
      IDLE: 50,
      PENDING: 200,
      PAUSED: 100,
      SCHEDULED_FOLLOW_UP: 0,
      FOLLOWED_UP: 0,
      OPTED_OUT: 0,
      FINISHED: 0,
    },
  },
  {
    portfolioId: "3",
    portfolioName: "Portfólio 3",
    executionStatus: PortfolioExecutionStatus.EXECUTING,
    totalItems: 2000,
    stats: {
      IN_PROGRESS: 200,
      SUCCEED: 400,
      FAILED: 600,
      CANCELLED: 800,
      UNLINKED: 1000,
      IDLE: 100,
      PENDING: 400,
      PAUSED: 200,
      SCHEDULED_FOLLOW_UP: 0,
      FOLLOWED_UP: 0,
      OPTED_OUT: 0,
      FINISHED: 0,
    },
  },
];
