import { Fetch } from '@/_interceptors';
import getEnv from '../_utils/hooks/getEnv';

export interface ICustomerWorkflow {
  id?: string;
  customerId: string;
  workflowId: string;
  workflowName: string;
  createdAt?: Date;
  updatedAt?: Date;
}

const transcendenceServiceEndpoint = getEnv('TRANSCENDENCE_SERVICE_URL');

export async function getAllWorkflows(
  customerId: string,
): Promise<ICustomerWorkflow[]> {
  if (getEnv('SERVICE_MOCK_RESPONSE')) {
    return mockedWorkflows;
  }

  const fetch = Fetch();

  const data = await fetch.get(
    transcendenceServiceEndpoint +
      `/v1/business-base/customers/${customerId}/workflows`,
  );

  return data as ICustomerWorkflow[];
}

export const mockedWorkflows: ICustomerWorkflow[] = [
  {
    id: '6fc9ad4c-ce4d-4e49-8882-2e351a367c94',
    customerId: '346b80ae-a77f-41cd-9d0a-854d3de5fd31',
    workflowId: '43e3ceed-58da-4ff2-bddf-67cb79d4433f',
    workflowName: 'Debt Negotiation Workflow',
  },
  {
    id: '1625bcf7-7907-46b5-808b-1c2909f21be8',
    customerId: '346b80ae-a77f-41cd-9d0a-854d3de5fd31',
    workflowId: 'c23e76d1-7713-4325-91ff-4bd660d4d2f0',
    workflowName: 'Call center workflow',
  },
];
