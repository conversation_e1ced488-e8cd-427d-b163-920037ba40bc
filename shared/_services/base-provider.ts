import getEnv from '../_utils/hooks/getEnv';
import parseError, { ParsedError } from '../_utils/parseError';

export interface IFetchFactoryExtendedOptions<T> extends RequestInit {
  hydrate?: (data?: any) => T;
}

export interface DataOrErrorResponse<T> {
  error: ParsedError | null;
  data: T | null;
}

export function fallbackWithMockData<T, Tout>(
  req: DataOrErrorResponse<any>,
  mockDataSource: T,
  transformData?: (data: T) => T | Tout | undefined | null,
): DataOrErrorResponse<Tout> {
  let shouldUseMock = false;

  if (!getEnv('SERVICE_MOCK_RESPONSE')) {
    return req;
  }

  const reqObj = req as DataOrErrorResponse<{ id: string }>;

  if (
    reqObj.data == null ||
    (Array.isArray(reqObj.data) &&
      reqObj.data.length === 0 &&
      reqObj.error != null)
  ) {
    const errMsg = reqObj.error!.message.toLowerCase();
    const errCode = reqObj.error!.code.toLowerCase();
    if (errMsg.includes('fail') && errMsg.includes('fetch')) {
      shouldUseMock = true;
    }
    if (errCode.includes('refuse')) {
      shouldUseMock = true;
    }
  }

  if (!shouldUseMock) {
    return req;
  }

  if (typeof transformData === 'function') {
    const parsedMock = transformData(mockDataSource) as Tout;

    let transformSuccess = true;
    if (Array.isArray(parsedMock) && parsedMock.length === 0) {
      transformSuccess = false;
    }
    if (!parsedMock) transformSuccess = false;

    if (transformSuccess) {
      return { data: parsedMock, error: null };
    } else {
      return { data: null, error: parseError('Not found') };
    }
  }

  return { data: mockDataSource as any, error: null };
}
