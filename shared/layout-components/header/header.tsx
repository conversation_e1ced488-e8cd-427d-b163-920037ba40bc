"use client";
import React, { ReactNode, useEffect } from "react";
import Link from "next/link";
import { ThemeChanger } from "../../redux/action";
import { connect } from "react-redux";
import { BiCog } from "react-icons/bi";
import { TbHeadset, TbLogout } from "react-icons/tb";
import { useQuery } from "@tanstack/react-query";
import Modalsearch from "../modal-search/modalsearch";
import store from "@/shared/redux/store";
import { getCustomer, Customer } from "@/shared/_services/customer.provider";
import { FidelEasyLogo, CollectCashLogo, SalesZapLogo } from "@/public/assets";
import { useRouter } from "next/navigation";
import { Fetch } from "@/_interceptors";

const basePath = "";

// TODO: #536485

const Header = ({ local_varaiable, ThemeChanger }: any) => {

  const router = useRouter();
  const handleLogout = async () => {
    try {
      const fetch = Fetch();
      const response = await fetch.post(
        `${process.env.NEXT_PUBLIC_TRANSCENDENCE_SERVICE_URL}/v1/auth/session/logout`
      );
  
      if (response.includes("User successfully logged out")) {
        localStorage.removeItem("cardsMetricsFilters");
        localStorage.clear();
        sessionStorage.clear();
        router.replace("/");
      } else {
        console.error("Falha ao fazer logout:", response.data || "Resposta sem corpo");
      }
    } catch (error) {
      console.error("Erro ao fazer logout:", error);
    }
  };

  const customerQuery = useQuery({
    queryKey: ["getCustomer"],
    queryFn: getCustomer,
  });
  const customer = (customerQuery.data || {}) as Customer;

  const iconSvg: { [key: string]: ReactNode } = {
    FIDELEASY: <FidelEasyLogo />,
    COLLECTCASH: <CollectCashLogo />,
    SALESZAP: <SalesZapLogo />,
  };

  function menuClose() {
    const theme = store.getState();
    if (window.innerWidth <= 992) {
      ThemeChanger({ ...theme, dataToggled: "close" });
    }
    if (window.innerWidth >= 992) {
      ThemeChanger({
        ...theme,
        dataToggled: local_varaiable.dataToggled
          ? local_varaiable.dataToggled
          : "",
      });
    }
  }

  const toggleSidebar = () => {
    const theme = store.getState();

    let sidemenuType = theme.dataNavLayout;
    if (window.innerWidth >= 992) {
      if (sidemenuType === "vertical") {
        let verticalStyle = theme.dataVerticalStyle;
        const navStyle = theme.dataNavStyle;
        switch (verticalStyle) {
          // closed
          case "closed":
            ThemeChanger({ ...theme, dataNavStyle: "" });
            if (theme.dataToggled === "close-menu-close") {
              ThemeChanger({ ...theme, dataToggled: "" });
            } else {
              ThemeChanger({ ...theme, dataToggled: "close-menu-close" });
            }
            break;
          // icon-overlay
          case "overlay":
            ThemeChanger({ ...theme, dataNavStyle: "" });
            if (theme.dataToggled === "icon-overlay-close") {
              ThemeChanger({ ...theme, dataToggled: "", iconOverlay: "" });
            } else {
              if (window.innerWidth >= 992) {
                ThemeChanger({
                  ...theme,
                  dataToggled: "icon-overlay-close",
                  iconOverlay: "",
                });
              }
            }
            break;
          // icon-text
          case "icontext":
            ThemeChanger({ ...theme, dataNavStyle: "" });
            if (theme.dataToggled === "icon-text-close") {
              ThemeChanger({ ...theme, dataToggled: "" });
            } else {
              ThemeChanger({ ...theme, dataToggled: "icon-text-close" });
            }
            break;
          // doublemenu
          case "doublemenu":
            ThemeChanger({ ...theme, dataNavStyle: "" });
            ThemeChanger({ ...theme, dataNavStyle: "" });
            if (theme.dataToggled === "double-menu-open") {
              ThemeChanger({ ...theme, dataToggled: "double-menu-close" });
            } else {
              let sidemenu = document.querySelector(".side-menu__item.active");
              if (sidemenu) {
                ThemeChanger({ ...theme, dataToggled: "double-menu-open" });
                if (sidemenu.nextElementSibling) {
                  sidemenu.nextElementSibling.classList.add(
                    "double-menu-active"
                  );
                } else {
                  ThemeChanger({ ...theme, dataToggled: "double-menu-close" });
                }
              }
            }
            // doublemenu(ThemeChanger);
            break;
          // detached
          case "detached":
            if (theme.dataToggled === "detached-close") {
              ThemeChanger({ ...theme, dataToggled: "", iconOverlay: "" });
            } else {
              ThemeChanger({
                ...theme,
                dataToggled: "detached-close",
                iconOverlay: "",
              });
            }

            break;

          // default
          case "default":
            ThemeChanger({ ...theme, dataToggled: "" });
        }
        switch (navStyle) {
          case "menu-click":
            if (theme.dataToggled === "menu-click-closed") {
              ThemeChanger({ ...theme, dataToggled: "" });
            } else {
              ThemeChanger({ ...theme, dataToggled: "menu-click-closed" });
            }
            break;
          // icon-overlay
          case "menu-hover":
            if (theme.dataToggled === "menu-hover-closed") {
              ThemeChanger({ ...theme, dataToggled: "" });
            } else {
              ThemeChanger({ ...theme, dataToggled: "menu-hover-closed" });
            }
            break;
          case "icon-click":
            if (theme.dataToggled === "icon-click-closed") {
              ThemeChanger({ ...theme, dataToggled: "" });
            } else {
              ThemeChanger({ ...theme, dataToggled: "icon-click-closed" });
            }
            break;
          case "icon-hover":
            if (theme.dataToggled === "icon-hover-closed") {
              ThemeChanger({ ...theme, dataToggled: "" });
            } else {
              ThemeChanger({ ...theme, dataToggled: "icon-hover-closed" });
            }
            break;
        }
      }
    } else {
      if (theme.dataToggled === "close") {
        ThemeChanger({ ...theme, dataToggled: "open" });

        setTimeout(() => {
          if (theme.dataToggled == "open") {
            const overlay = document.querySelector("#responsive-overlay");

            if (overlay) {
              overlay.classList.add("active");
              overlay.addEventListener("click", () => {
                const overlay = document.querySelector("#responsive-overlay");

                if (overlay) {
                  overlay.classList.remove("active");
                  menuClose();
                }
              });
            }
          }

          window.addEventListener("resize", () => {
            if (window.screen.width >= 992) {
              const overlay = document.querySelector("#responsive-overlay");

              if (overlay) {
                overlay.classList.remove("active");
              }
            }
          });
        }, 100);
      } else {
        ThemeChanger({ ...theme, dataToggled: "close" });
      }
    }
  };
  //Dark Model

  const ToggleDark = () => {
    ThemeChanger({
      ...local_varaiable,
      class: local_varaiable.class == "dark" ? "light" : "dark",
      dataHeaderStyles: local_varaiable.class == "dark" ? "light" : "dark",
      dataMenuStyles:
        local_varaiable.dataNavLayout == "horizontal"
          ? local_varaiable.class == "dark"
            ? "light"
            : "dark"
          : "dark",
    });
    const theme = store.getState();

    if (theme.class != "dark") {
      ThemeChanger({
        ...theme,
        bodyBg: "",
        Light: "",
        darkBg: "",
        inputBorder: "",
      });
      localStorage.setItem("ynexlighttheme", "light");
      localStorage.removeItem("ynexdarktheme");
      localStorage.removeItem("ynexMenu");
      localStorage.removeItem("ynexHeader");
    } else {
      localStorage.setItem("ynexdarktheme", "dark");
      localStorage.removeItem("ynexlighttheme");
      localStorage.removeItem("ynexMenu");
      localStorage.removeItem("ynexHeader");
    }
  };

  //#region Header Elements

  const headElm_Profile = (
    <div className="header-element px-2 md:ps-4 md:pe-0 hs-dropdown !items-center ti-dropdown [--placement:bottom-left]">
      <div className="md:block hidden dropdown-profile">
        <p className="font-semibold mb-0 leading-tighter text-[#536485] text-[0.813rem] text-right">
          {customer.name || "User Name"}
        </p>
        <span className="opacity-[0.7] font-normal leading-tighter text-[#536485] block text-[0.6875rem] text-right">
          {customer.segment || "Segment"}
        </span>
      </div>
      <button
        id="dropdown-profile"
        type="button"
        className="hs-dropdown-toggle ti-dropdown-toggle !gap-2 !p-0 flex-shrink-0 sm:mx-2 me-0 !rounded-full !shadow-none text-xs align-middle !border-0 !shadow-transparent"
      >
        <img
          className="inline-block rounded-full bg-slate-200"
          src={`${process.env.NODE_ENV === "production" ? basePath : ""}/assets/images/_avatar/doctor.png`}
          width="32"
          height="32"
          alt=""
        />
      </button>

      <div
        className="hs-dropdown-menu ti-dropdown-menu !-mt-3 border-0 w-[11rem] !p-0 border-defaultborder hidden main-header-dropdown pt-0 overflow-hidden header-profile-dropdown dropdown-menu-end"
        aria-labelledby="dropdown-profile"
      >
        <ul className="text-defaulttextcolor font-medium dark:text-[#8c9097] dark:text-white/50">
          {/* <li>
            <Link className="w-full ti-dropdown-item !text-[0.8125rem] !gap-x-0  !p-[0.65rem]" href="/customer">
              <TbUserCircle className="text-[1.125rem] me-2 opacity-[0.7] !inline-flex" />
              Profile
            </Link>
          </li>          */}
          <li>
            <Link
              className="w-full ti-dropdown-item !text-[0.8125rem] !p-[0.65rem] !gap-x-0 !inline-flex"
              href="#!"
            >
              <TbHeadset className="text-[1.125rem] me-2 opacity-[0.7] !inline-flex" />
              Support
            </Link>
          </li>
          <li>
            <span
              className="w-full ti-dropdown-item !text-[0.8125rem] !p-[0.65rem] !gap-x-0 !inline-flex cursor-pointer"
              onClick={() => handleLogout()}
            >
              <TbLogout className="text-[1.125rem] me-2 opacity-[0.7] !inline-flex" />
              Log Out
            </span>
          </li>
        </ul>
      </div>
    </div>
  );

  const headElm_SwitcherBtn = (
    <div className="header-element md:px-[0.48rem]">
      <button
        aria-label="button"
        type="button"
        className="hs-dropdown-toggle switcher-icon inline-flex flex-shrink-0 justify-center items-center gap-2  rounded-full font-medium  align-middle transition-all text-xs dark:text-[#8c9097] dark:text-white/50 dark:hover:text-white dark:focus:ring-white/10 dark:focus:ring-offset-white/10"
        data-hs-overlay="#hs-overlay-switcher"
      >
        <BiCog className="header-link-icon animate-spin-slow" />
      </button>
    </div>
  );

  //#endregion

  useEffect(() => {
    const handleResize = () => {
      const windowObject = window;
      if (windowObject.innerWidth <= 991) {
      } else {
      }
    };
    handleResize(); // Check on component mount
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    const navbar = document?.querySelector(".header");
    const navbar1 = document?.querySelector(".app-sidebar");
    const sticky: any = navbar?.clientHeight;
    // const sticky1 = navbar1.clientHeight;

    function stickyFn() {
      if (window.scrollY >= sticky) {
        navbar?.classList.add("sticky-pin");
        navbar1?.classList.add("sticky-pin");
      } else {
        navbar?.classList.remove("sticky-pin");
        navbar1?.classList.remove("sticky-pin");
      }
    }

    window.addEventListener("scroll", stickyFn);
    window.addEventListener("DOMContentLoaded", stickyFn);

    // Cleanup event listeners when the component unmounts
    return () => {
      window.removeEventListener("scroll", stickyFn);
      window.removeEventListener("DOMContentLoaded", stickyFn);
    };
  }, []);

  return (
    <>
      <div className="app-header">
        <nav className="main-header !h-[3.75rem]" aria-label="Global">
          <div className="main-header-container ps-[0.725rem] pe-[1rem] ">
            <div className="header-content-left">
              <div className="header-element">
                <div className="horizontal-logo">
                  <Link href="/portfolio" className="header-logo">
                    {
                      iconSvg[
                        process.env.NEXT_PUBLIC_SITE_NAME || "COLLECTCASH"
                      ]
                    }
                  </Link>
                </div>
              </div>
              <button
                type="button"
                className="header-element md:px-[0.325rem] !items-center"
                onClick={() => toggleSidebar()}
              >
                <div
                  aria-label="Hide Sidebar"
                  className="sidemenu-toggle animated-arrow  hor-toggle horizontal-navtoggle inline-flex items-center"
                >
                  <span></span>
                </div>
              </button>
            </div>
            <div className="header-content-right">
              {/* {headElm_SearchBtn} */}
              {/* {headElm_I18nFlagsBtn} */}
              {/* {headElm_ThemeModeBtn} */}
              {/* headElm_Cart */}
              {/* {headElm_Notifications} */}
              {/* headElm_Apps */}
              {/* {headElm_Fullscreen} */}
              {headElm_Profile}
              {/* {headElm_SwitcherBtn} */}
            </div>
          </div>
        </nav>
      </div>
      <Modalsearch />
    </>
  );
};

const mapStateToProps = (state: any) => ({
  local_varaiable: state,
});
export default connect(mapStateToProps, { ThemeChanger })(Header);
