import React, { useEffect } from "react";
import { RiArrowUpSFill } from "react-icons/ri";

const Backtotop = () => {
  const screenUp = () => {
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    const handleScroll = () => {
      const color: any = document.getElementsByClassName("scrollToTop")[0];
      if (color) {
        window.scrollY > 100 ? (color.style.display = "flex") : (color.style.display = "none");
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  return (
    <div className="scrollToTop" onClick={screenUp}>
      <span className="arrow">
        <RiArrowUpSFill className="text-xl" />
      </span>
    </div>
  );
};

export default Backtotop;
