import { ReactNode } from "react";
import {
  LuFileBox,
  LuShare2,
  LuPackage,
  LuFileClock,
  LuFileCog2,
  LuFileCheck,
  LuHourglass,
  LuPlusSquare,
  LuFilePlus,
  LuChevronDownCircle,
  LuSave,
  LuFile,
  LuFileSpreadsheet,
  LuUploadCloud,
  LuLayoutList,
  LuRefreshCw,
  LuRefreshCcwDot,
  LuXCircle,
  LuRefreshCwOff,
  LuUserCircle2,
  LuImage,
  LuArrowLeft,
  LuDownload,
  LuSettings,
  LuSend,
} from "react-icons/lu";
import {
  MdOutlineForum,
  MdOutlineSmartToy,
  MdPause,
  MdTranscribe,
  MdPlayArrow,
  MdEmojiEmotions,
  MdDoDisturbAlt,
} from "react-icons/md";
import { CgSandClock } from "react-icons/cg";
import { VscDebugStepOver, VscTools } from "react-icons/vsc";
import { BsStopCircleFill, BsTrash3Fill } from "react-icons/bs";
import { FaRegCheckCircle } from "react-icons/fa";
import {
  TbMessageCircleBolt,
  TbDotsVertical,
  TbPlugConnected,
  TbPlugConnectedX,
  TbMessageUp,
  TbMessageDown,
  TbMessagePause,
  TbFlagFilled,
  TbMessageCancel,
  TbUnlink,
  TbMessageQuestion,
  TbPlayerTrackNext,
} from "react-icons/tb";
import { GoBlocked } from "react-icons/go";
import { GrDocumentPerformance } from "react-icons/gr";
import { GiBugleCall, GiCargoCrane } from "react-icons/gi";
import { IconType } from "react-icons/lib";

import {
  PortfolioExecutionStatus,
  PortfolioImportStatus,
} from "../_services/portfolio.provider";
import { PortfolioItemStatus } from "../_services/portfolio-item.provider";
import { CollectCashLogo, FidelEasyLogo, SalesZapLogo } from "@/public/assets";

const iconSvg: { [key: string]: ReactNode } = {
  FIDELEASY: <FidelEasyLogo />,
  COLLECTCASH: <CollectCashLogo />,
  SALESZAP: <SalesZapLogo />,
};

export const IconAppLogo =
  iconSvg[process.env.NEXT_PUBLIC_SITE_NAME || "COLLECTCASH"];
export const IconForIcon = LuImage;

//

export const IconPortfolioFile = LuFileBox;
export const IconPortfolioPerformance = GrDocumentPerformance;
export const IconPortfolioEntity = LuPackage;
// export const IconPortfolioEntity = MdOutlineMenuBook;
export const IconNewPortfolio = LuFilePlus;

export const IconPortfolioImportStatusEnum: { [key: string]: IconType } = {
  [PortfolioImportStatus.UPLOADED]: LuFileClock,
  [PortfolioImportStatus.PROCESSING]: LuFileCog2,
  [PortfolioImportStatus.SUCCESS]: LuFileCheck,
};
export const ColorPortfolioImportStatusEnum: {
  [key: string]: { text: string; bg: string };
} = {
  [PortfolioImportStatus.UPLOADED]: {
    text: "text-secondary",
    bg: "bg-secondary",
  },
  [PortfolioImportStatus.PROCESSING]: {
    text: "text-warning",
    bg: "bg-warning",
  },
  [PortfolioImportStatus.SUCCESS]: { text: "text-success", bg: "bg-success" },
};

export const IconPortfolioExecutionStatusEnum: { [key: string]: IconType } = {
  [PortfolioExecutionStatus.EXECUTING]: TbMessageCircleBolt,
  [PortfolioExecutionStatus.QUEUED]: VscDebugStepOver,
  [PortfolioExecutionStatus.PAUSED]: MdPause,
  [PortfolioExecutionStatus.WAITING]: LuHourglass,
  [PortfolioExecutionStatus.CANCELLED]: GoBlocked,
  [PortfolioExecutionStatus.INBOUND]: TbMessageCircleBolt,
  [PortfolioExecutionStatus.FINISHED]: FaRegCheckCircle,
};
export const ColorPortfolioExecutionStatusEnum: {
  [key: string]: { text: string; bg: string };
} = {
  [PortfolioExecutionStatus.EXECUTING]: {
    text: "text-teal-900",
    bg: "bg-teal-400",
  },
  [PortfolioExecutionStatus.QUEUED]: {
    text: "text-orange-900",
    bg: "bg-orange-200",
  },
  [PortfolioExecutionStatus.PAUSED]: {
    text: "text-orange-900",
    bg: "bg-orange-400",
  },
  [PortfolioExecutionStatus.WAITING]: {
    text: "text-orange-900",
    bg: "bg-orange-400",
  },
  [PortfolioExecutionStatus.CANCELLED]: {
    text: "text-red-900",
    bg: "bg-red-400",
  },
  [PortfolioExecutionStatus.INBOUND]: {
    text: "text-teal-900",
    bg: "bg-teal-400",
  },
  [PortfolioExecutionStatus.FINISHED]: {
    text: "text-green-900",
    bg: "bg-green-400",
  },
};

export const IconNegotiation = MdOutlineForum;

export const IconPortfolioItemStatusEnum: { [key: string]: IconType } = {
  [PortfolioItemStatus.PENDING]: CgSandClock,
  [PortfolioItemStatus.IN_PROGRESS]: MdTranscribe,
  [PortfolioItemStatus.PAUSED]: TbMessagePause,
  [PortfolioItemStatus.FAILED]: BsStopCircleFill,
  [PortfolioItemStatus.SUCCEED]: TbFlagFilled,
  [PortfolioItemStatus.CANCELLED]: TbMessageCancel,
  [PortfolioItemStatus.UNLINKED]: TbUnlink,
  [PortfolioItemStatus.IDLE]: TbMessageQuestion,
  [PortfolioItemStatus.FOLLOWED_UP]: TbMessageQuestion,
  [PortfolioItemStatus.SCHEDULED_FOLLOW_UP]: TbMessageQuestion,
  [PortfolioItemStatus.OPTED_OUT]: TbPlayerTrackNext,
  [PortfolioItemStatus.FINISHED]: BsStopCircleFill,
};

export const ColorPortfolioItemStatusEnum: {
  [key: string]: { text: string; bg: string };
} = {
  [PortfolioItemStatus.PENDING]: { text: "text-slate-800", bg: "bg-slate-200" },
  [PortfolioItemStatus.IN_PROGRESS]: {
    text: "text-orange-700",
    bg: "bg-orange-100",
  },
  [PortfolioItemStatus.PAUSED]: { text: "text-stone-600", bg: "bg-stone-300" },
  [PortfolioItemStatus.FAILED]: { text: "text-green-900", bg: "bg-green-300" },
  [PortfolioItemStatus.SUCCEED]: {
    text: "text-indigo-900",
    bg: "bg-indigo-100",
  },
  [PortfolioItemStatus.CANCELLED]: {
    text: "text-purple-800",
    bg: "bg-purple-100",
  },
  [PortfolioItemStatus.UNLINKED]: { text: "text-cyan-900", bg: "bg-cyan-200" },
  [PortfolioItemStatus.IDLE]: { text: "text-yellow-900", bg: "bg-yellow-200" },
  [PortfolioItemStatus.FOLLOWED_UP]: {
    text: "text-blue-900",
    bg: "bg-blue-200",
  },
  [PortfolioItemStatus.SCHEDULED_FOLLOW_UP]: {
    text: "text-blue-900",
    bg: "bg-blue-200",
  },
  [PortfolioItemStatus.OPTED_OUT]: { text: "text-red-900", bg: "bg-red-200" },
  [PortfolioItemStatus.FINISHED]: {
    text: "text-yellow-900",
    bg: "bg-yellow-200",
  },
};

export const IconWorkflow = MdOutlineSmartToy;
export const IconManageWorkflow = GiCargoCrane;

export const IconIntegration = LuShare2;
export const IconIntegrationManage = VscTools;
export const IconConnect = TbPlugConnected;
export const IconDisconnect = TbPlugConnectedX;
export const IconTestIntegration = GiBugleCall;

export const IconMessageSent = TbMessageUp;
export const IconMessageReceived = TbMessageDown;

export const IconCustomer = LuUserCircle2;
//

export const IconMenuMore = TbDotsVertical;
export const IconSelectedFile = LuFile;
export const IconDelete = BsTrash3Fill;
export const IconSelectedFileCSV = LuFileSpreadsheet;
export const IconChevronSelect = LuChevronDownCircle;
export const IconUpload = LuUploadCloud;
export const IconDownload = LuDownload;
export const IconSave = LuSave;
export const IconSuccess = LuFileCheck;
export const IconNew = LuPlusSquare;
export const IconListing = LuLayoutList;
export const IconRefresh = LuRefreshCw;
export const IconAutoRefreshOn = LuRefreshCcwDot;
export const IconAutoRefreshOff = LuRefreshCwOff;
export const IconClose = LuXCircle;
export const IconReturn = LuArrowLeft;
export const IconPause = MdPause;
export const IconPlay = MdPlayArrow;
export const IconSettings = LuSettings;
export const IconSendPlane = LuSend;
export const IconEmoji = MdEmojiEmotions;
