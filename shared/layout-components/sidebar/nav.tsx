import React from 'react';
import {
  IconIntegration,
  IconNegotiation,
  IconPortfolioEntity,
  IconPortfolioFile,
  IconPortfolioPerformance,
  IconWorkflow,
} from '../icons';
import { BiLayer } from 'react-icons/bi';
import { MdOutlineWidgets } from 'react-icons/md';

const badge = (
  <span className='badge !bg-warning/10 !text-warning !py-[0.25rem] !px-[0.45rem] !text-[0.75em] ms-1'>
    12
  </span>
);
const badge1 = (
  <span className='text-secondary text-[0.75em] rounded-sm !py-[0.25rem] !px-[0.45rem] badge !bg-secondary/10 ms-1'>
    New
  </span>
);
const badge2 = (
  <span className='text-danger text-[0.75em] rounded-sm badge !py-[0.25rem] !px-[0.45rem] !bg-danger/10 ms-1'>
    Hot
  </span>
);
const badge4 = (
  <span className='text-success text-[0.75em] badge !py-[0.25rem] !px-[0.45rem] rounded-sm bg-success/10 ms-1'>
    3
  </span>
);

export const MenuItems: any = [
  {
    menutitle: 'Digai Negocia',
  },

  {
    icon: (
      <IconPortfolioPerformance
        className='side-menu__icon'
        style={{ fill: 'none' }}
      />
    ),
    path: '/dashboard',
    type: 'link',
    active: false,
    selected: false,
    dirchange: false,
    title: 'Performance',
  },
  {
    icon: (
      <IconPortfolioFile className='side-menu__icon' style={{ fill: 'none' }} />
    ),
    path: '/portfolio/imports',
    type: 'link',
    active: false,
    selected: false,
    dirchange: false,
    title: 'Importações',
  },

  // {
  //   icon: <IconPortfolioEntity className="side-menu__icon" style={{ fill: "none" }} />,
  //   title: "Portfolio",
  //   selected: false,
  //   active: false,
  //   type: "sub",
  //   children: [
  //   ],
  // },
  {
    icon: <IconNegotiation className='side-menu__icon' />,
    title: 'Negociações',
    type: 'link',
    active: false,
    selected: false,
    path: '/negotiation',
  },
  {
    icon: <IconIntegration className='side-menu__icon' />,
    title: 'Integrações',
    type: 'link',
    active: false,
    selected: false,
    path: '/integration',
  },
];
