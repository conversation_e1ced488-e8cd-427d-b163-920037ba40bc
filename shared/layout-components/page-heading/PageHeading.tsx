"use client";

import { PropsWithChildren, ReactNode } from "react";
import { twMerge } from "tailwind-merge";

export interface IPageHeadingProps extends Partial<PropsWithChildren<JSX.IntrinsicElements["header"]>> {
  icon?: ReactNode;
  primary?: ReactNode;
  secondary?: ReactNode;
  // actions buttons etc
  // statuses
}

export default function PageHeading(props: IPageHeadingProps) {
  const { className, icon, children, primary, secondary, ...restProps } = props;

  return (
    <header {...restProps} className={twMerge("PageHeading", "flex flex-col md:flex-row items-start gap-2 p-4", className)}>
      <div className="flex-auto flex items-center gap-2 text-2xl font-semibold w-full">
        {icon && <div className="PageHeading-icon opacity-70 text-3xl flex items-center justify-center">{icon}</div>}

        {primary && <div className="PageHeading-primary">{primary}</div>}
      </div>

      {secondary && <div className="PageHeading-secondary flex flex-col items-end gap-2 min-w-32 w-full">{secondary}</div>}

      {/* actions? actions. */}

      {/* statuses... */}
    </header>
  );
}
