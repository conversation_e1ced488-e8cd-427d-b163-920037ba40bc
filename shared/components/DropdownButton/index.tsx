import { ReactNode } from 'react';
import { <PERSON><PERSON>, <PERSON>u<PERSON>utton, Menu<PERSON>tem, MenuItems } from '@headlessui/react';

type Props = {
  icon?: ReactNode;
  label: string;
  options: {
    label: string;
    key: string;
    href?: string;
    onClick?: () => void;
  }[];
};

export function DropdownButton({ icon, label, options }: Props) {
  return (
    <Menu as='div' className='relative inline-block text-left'>
      <div>
        <MenuButton className='inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50'>
          {label}
          {icon}
        </MenuButton>
      </div>

      <MenuItems
        transition
        className='absolute  right-0 z-10 mt-2 w-48 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in'
      >
        {options.map((option) => (
          <div key={option.key} className='py-1'>
            {option.href ? (
              <MenuItem>
                <a
                  href={option.href}
                  onClick={option.onClick}
                  rel='noopener noreferrer'
                  className='block px-4 py-2 text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:text-gray-900 data-[focus]:outline-none'
                >
                  {option.label}
                </a>
              </MenuItem>
            ) : (
              <MenuItem>
                <button
                  onClick={option.onClick}
                  className='block px-4 w-full text-left py-2 text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:text-gray-900 data-[focus]:outline-none'
                >
                  {option.label}
                </button>
              </MenuItem>
            )}
          </div>
        ))}
      </MenuItems>
    </Menu>
  );
}
