import { useState, useEffect } from 'react';
import Select from 'react-select';

type Props = {
  label: string;
  name: string;
  list: { label: string; key: string; value: string }[];
  selectedOption: string;
  handleSelect: (filterValue: string) => void;
  disabled?: boolean;
};

export function SelectFilter({
  label,
  name,
  list,
  selectedOption,
  handleSelect,
  disabled,
}: Props) {
  const optionsList = [{ key: 'todos', label: 'Todos', value: '' }, ...list];

  const selected = selectedOption
    ? optionsList.find((option) => option.value === selectedOption)
    : optionsList[0];

  return (
    <div className='xl:col-span-4 col-span-12'>
      <div className='box m-0!'>
        <div className='box-header'>
          <label className='box-title'>{label}</label>
        </div>
        <div className='box-body'>
          <select
            id={name}
            name={name}
            className='js-example-placeholder-multiple w-full js-states hs-dropdown-select border-gray-200 rounded-sm text-sm'
            onChange={(e) => handleSelect(e.target.value)}
            value={selected?.value}
            disabled={disabled}
          >
            {optionsList.map((option) => (
              <option key={option.key} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}
