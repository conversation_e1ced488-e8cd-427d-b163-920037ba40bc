import { ReactNode } from 'react';

type Props = {
  details: { key: string; label: string; value: ReactNode }[];
};

export function DetailsList({ details }: Props) {
  return (
    <dl className='divide-y divide-gray-300'>
      {details.map((item) => (
        <div
          key={item.key}
          className='px-2 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0'
        >
          <dt className='text-sm/6 font-medium text-gray-900'>{item.label}</dt>
          <dd className='mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0'>
            {item.value}
          </dd>
        </div>
      ))}
    </dl>
  );
}
