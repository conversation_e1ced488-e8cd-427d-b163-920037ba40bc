import getRelativeDateSpelled, {
  GetRelativeDateSpelledOptions,
  DateishValue,
} from "./getRelativeDateSpelled";

type DateishValueOrNull = DateishValue | null;

export function formatDateAgo(
  val?: DateishValueOrNull,
  config?: GetRelativeDateSpelledOptions
) {
  if (!val || isNaN(+new Date(val))) return "- n/a -";

  const parts = getRelativeDateSpelled(val, {
    locale: "pt-BR",
    joinLastPartsStr: ", ",
    style: "short",
    maxParts: 2,
    ...(config || {}),
  });
  return parts;
}

export function formatRelativeDate(createdAt: string): string {
  const createdDate = new Date(createdAt);
  const now = new Date();
  const diffMs = now.getTime() - createdDate.getTime();

  const minutes = Math.floor(diffMs / (1000 * 60)) % 60;
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (minutes < 1) {
    return "1 minuto";
  } else if (hours < 1) {
    return `${minutes} minutos`;
  } else if (days < 1) {
    const hourStr = hours === 1 ? "1 hora" : `${hours} horas`;
    const minuteStr = minutes === 1 ? "1 minuto" : `${minutes} minutos`;
    return `${hourStr} e ${minuteStr}`;
  } else {
    return days === 1 ? "1 dia" : `${days} dias`;
  }
}

export function formatDateAgoFactory(
  baseConfig: GetRelativeDateSpelledOptions
): (val?: DateishValueOrNull) => string {
  return function formatter(val?: DateishValueOrNull) {
    return formatDateAgo(val, baseConfig);
  };
}

export function formatDateSimple(
  valDate?: DateishValueOrNull,
  which: "both" | "MMM DD" | "date" | "time" = "both"
) {
  let datePart = "__/__/____";
  let timePart = "__:__";
  let MMMDDPart = "? - ??";

  if (Boolean(valDate)) {
    const objDate = new Date(valDate as any);

    if (!isNaN(+objDate)) {
      datePart = objDate.toLocaleDateString("pt-BR");
      timePart = objDate.toLocaleTimeString("pt-BR").slice(0, 5);
      MMMDDPart = objDate.toDateString().slice(4, 10);
    }
  }

  if (which === "MMM DD") return MMMDDPart;
  if (which === "date") return datePart;
  if (which === "time") return timePart;
  return datePart + " " + timePart;
}
