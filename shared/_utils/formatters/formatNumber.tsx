export function formatNumber(val?: any, decimals: number = 2) {
  const decimalFormatter = new Intl.NumberFormat("pt-BR", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: 2,
  });
  if (!val) return "0";
  if (isNaN(+val)) return "??";
  return decimalFormatter.format(+val);
}

//

const percentFormatter = new Intl.NumberFormat("pt-BR", {
  style: "percent",
  minimumFractionDigits: 2,
});

export function formatPercent(val?: any) {
  if (!val) return "0";
  if (isNaN(+val)) return "? ??%";
  return percentFormatter.format(+val);
}

export function formatPhoneNumber(phoneNumber: string): string {
  const countryCode = phoneNumber.slice(0, 2);
  const areaCode = phoneNumber.slice(2, 4);
  const localNumber = phoneNumber.slice(4);

  let formattedLocalNumber;
  if (localNumber.length === 8) {
    const firstPart = localNumber.slice(0, 4);
    const secondPart = localNumber.slice(4, 8);
    formattedLocalNumber = `${firstPart}-${secondPart}`;
  } else {
    const firstPart = localNumber.slice(0, 5);
    const secondPart = localNumber.slice(5, 9);
    formattedLocalNumber = `${firstPart}-${secondPart}`;
  }

  return `+${countryCode} (${areaCode}) ${formattedLocalNumber}`;
}

const fmtr = new Intl.NumberFormat("pt-BR", {
  //
  //
  // style: "decimal", // decimal  |  currency  |  percent  |  unit
  //
  //
  // minimumIntegerDigits: 1,           // 1..21   (INTEGERS.________)
  //
  // minimumFractionDigits: 0,          // 0..100  (________.FRACTION)
  // maximumFractionDigits: 3,          // default max(3, minimumFractionDigits)
  //
  //
  // ---------------------------------------------------------------------------
  //
  //
  // roundingIncrement: 1,              // 1, 2, 5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000, 2500, and 5000
  //                                    // or
  // minimumSignificantDigits: 1,       // 0,000,001,234,567.89
  // maximumSignificantDigits: 21,      // 1,234,567.89
  // roundingPriority: "auto",          // auto  |  lessPrecision  |  morePrecision
  //
  // notation: "standard",              // standard  |  scientific (1.2e3)  |  engineering (e10 if e%3==0)  |  compact (e)
  // compactDisplay: "short",           // short  |  long
  //
  /*
  | rounding Priority_ | notation______ | MAX_Significant | min_Significant | MAX_Fraction___ | min_Fraction___ |
  |- - - - - - - - - - | - - - - - - - -| - - - - - - - - | - - - - - - - - | - - - - - - - - | - - - - - - - - |
  |       set:   less  |                |            (!)  |            (!)  |            (!)  |            (!)  |
  |       set:   more  |                |            (!)  |            (!)  |            (!)  |            (!)  |
  |- - - - - - - - - - | - - - - - - - -| - - - - - - - - | - - - - - - - - | - - - - - - - - | - - - - - - - - |
  |  [auto]            |                |  set       (!)  |            (!)  |       ---       |       ---       |
  |  [auto]            |                |            (!)  |  set       (!)  |       ---       |       ---       |
  |- - - - - - - - - - | - - - - - - - -| - - - - - - - - | - - - - - - - - | - - - - - - - - | - - - - - - - - |
  |  [auto]            |                |       ---       |       ---       |  set       (!)  |            (!)  |
  |  [auto]            |                |       ---       |       ---       |            (!)  |  set       (!)  |
  |  [auto]            |  NOT  compact  |       ---       |       ---       |            (!)  |            (!)  |
  |- - - - - - - - - - | - - - - - - - -| - - - - - - - - | - - - - - - - - | - - - - - - - - | - - - - - - - - |
  |  [auto]  ->  more  |       compact  |          [ 2 ]  |          [ 1 ]  |          [ 1 ]  |          [ 0 ]  |
  |- - - - - - - - - - | - - - - - - - -| - - - - - - - - | - - - - - - - - | - - - - - - - - | - - - - - - - - |
  */
  //
  //
  // ---------------------------------------------------------------------------
  //
  //
  //
  // style: "currency",
  // currency: "BRL",                   // https://en.wikipedia.org/wiki/ISO_4217#List_of_ISO_4217_currency_codes
  // currencyDisplay: "symbol",         // code (BRL)  |  name (reais)  |  narrowSymbol ($)  |  symbol (US$)
  // currencySign: "standard",          // standard (-123)  |  accounting ((123))
  // minimumFractionDigits: 2,          // 0..100
  // maximumFractionDigits: 2,          // default max(2, minimumFractionDigits)
  //
  //
  // ---------------------------------------------------------------------------
  //
  //
  // style: "unit",
  // unit: "liter",                     // https://tc39.es/ecma402/#table-sanctioned-single-unit-identifiers
  // unitDisplay: "short",              // long (16 litres)  |  narrow (16l)  |  short (16 l)
  //
  //
  // ---------------------------------------------------------------------------
  //
  //
  // style: "percent",
  // maximumFractionDigits: 0,          // default max(0, minimumFractionDigits)
  //
  //
  // ---------------------------------------------------------------------------
  //
  //
  // useGrouping: "auto",               // auto  |  always == true  |  min2  |  false (1234567.89)
  // roundingMode: "halfExpand",        // ceil  |  floor  |  expand  |  trunc  |  halfCeil  |  halfFloor  |  halfExpand  |  halfTrunc  |  halfEven
  // trailingZeroDisplay: "auto",       // auto  |  stripIfInteger
  // signDisplay: "auto",               // auto ( 1,  0, -0, -1)  |  always (+1, +0, -0, -1)  |  exceptZero (+1,  0,  0, -1)  |  negative (1,  0,  0, -1)  |  never ( 1,  0,  0,  1)
});
