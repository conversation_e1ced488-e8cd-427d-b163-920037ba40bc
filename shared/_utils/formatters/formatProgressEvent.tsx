import formatFileSize from "./formatFileSize";

interface ProgEvt {
  total?: number;
  loaded?: number;
  rate?: number;
}

export default function formatProgressEvent(progressEvent?: ProgEvt | null) {
  const progEvt = (progressEvent || {}) as ProgEvt;

  const loaded: number = progEvt.loaded || 0;
  const total: number = progEvt.total || 0;
  const rate: number = progEvt.rate || 0;

  let ratio: number = 0;
  if (loaded !== 0 && total !== 0) {
    ratio = loaded / total;
    ratio = Math.floor(ratio * 10000) / 100;
  }

  return {
    loaded: formatFileSize(loaded),
    total: formatFileSize(total),
    rate: formatFileSize(rate),
    ratio: ratio.toFixed(2) + "%",
  };
}
