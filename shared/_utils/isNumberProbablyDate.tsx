import { DateishValue } from "./formatters/getRelativeDateSpelled";

const YEAR_IN_MS = 31556926000;
export default function isNumberProbablyDate(value?: DateishValue | null): boolean {
  const CURR_DELTA_YEARS = Date.now() / YEAR_IN_MS;
  
  if (!value || isNaN(+value)) return false;
  value = value as Date;

  // MS != 000: likely to be a broken number: 512, 15400, 120250, 1.234.567
  if (value.getUTCMilliseconds() !== 0) return false;
  // SEC != 00: likely to be a broken number: 512, 15400, 120250, 1.234.567
  if (value.getUTCSeconds() !== 0) return false;
  // This line will fail for "1970-01-01"
  if (Math.abs(0 - +value) < 40000) return false;
  // Amount of years since 1970
  const deltaYears = +value / YEAR_IN_MS;
  // If the date is after 2051, its likely a document 24.555.888/0001-94
  // or a phone number 55 11 90000 1234. Also catches if it's a portfolio with
  // numbers of trillionaire debts.
  // (this was written in 2024)
  if (deltaYears > CURR_DELTA_YEARS * 1.5) return false;
  // same as MS, but with extra check.
  if (Math.abs(0 - deltaYears) < 2) {
    if (value.getUTCHours() !== 0) return false;
    // Dates in CSV are probably like "YYYY-MM-DD", which would yield "00:00:00"
    if (value.getUTCMinutes() !== 0) return false;
  }

  return true;
}
