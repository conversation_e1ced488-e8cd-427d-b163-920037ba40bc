"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

export default function useQParams() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [data, setData] = useState({});

  useEffect(() => {
    setData(Object.fromEntries(searchParams.entries()));
  }, [searchParams]);

  function setAndGo(part: Record<string, any>, reset: boolean = false) {
    const newParams = new URLSearchParams({
      ...(reset ? {} : data),
      ...part,
    }).toString();

    router.push(pathname + "?" + newParams);
  }

  return {
    setAndGo,
    QParams: data,
    has: (name: string) => searchParams.has(name),
    get: (name: string) => searchParams.get(name),
  };
}
