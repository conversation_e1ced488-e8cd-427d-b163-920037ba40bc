function parseStringValue(str: string) {
  return (str + " ")
    .split("")
    .map(function (char) {
      return char.charCodeAt(0);
    })
    .reduce(function (a, b, i) {
      return a + b * i;
    });
}

function oscillate(value: number, min: number, max: number) {
  var range = max - min;
  return range == 0 ? max : min + Math.abs(((value + range) % (range * 2)) - range);
}

export interface IStrToColorConstraints {
  minSaturation: number;
  maxSaturation: number;
  minLuminosity: number;
  maxLuminosity: number;
  minHue: number;
  maxHue: number;
  offsetHue: number;
}

export default function strToColor(val: string, constraints?: IStrToColorConstraints): string {
  const {
    minSaturation = 40,
    maxSaturation = 100,
    minLuminosity = 15,
    maxLuminosity = 85,
    minHue = 0,
    maxHue = 360,
    offsetHue = 0,
  } = (constraints || {}) as IStrToColorConstraints;

  const str = val.toUpperCase();
  var textValue = parseStringValue(str);

  const saturation = oscillate(textValue, Math.min(100, maxSaturation), Math.max(0, minSaturation));
  textValue -= saturation;
  if (textValue < 0) textValue = parseStringValue(str);

  const luminosity = oscillate(textValue, Math.max(0, minLuminosity), Math.min(100, maxLuminosity));
  textValue -= luminosity;
  if (textValue < 0) textValue = parseStringValue(str);

  let hue = oscillate(textValue, Math.min(360, maxHue), Math.max(0, minHue)) + offsetHue;
  hue = hue < 0 ? hue + 360 : hue;
  hue = hue > 360 ? hue - 360 : hue;

  return "hsl(" + hue + "," + saturation + "%," + luminosity + "%)";
}
