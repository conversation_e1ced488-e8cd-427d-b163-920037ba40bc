export default async function readFileChunk(file: File, offset: number = 0, length: number = 1024): Promise<string> {
  const fileChunk = await new Promise<string>((resolve, reject) => {
    const fileReader = new FileReader();

    fileReader.onerror = (evt) => {
      console.error("Read file chunk failed", evt);
      reject("");
    };

    fileReader.onload = function (evt) {
      const chunkData = fileReader.result as string;
      console.debug("chunkData loaded", chunkData, fileReader, evt);
      resolve(chunkData);
    };

    const chunkStart = Math.max(0, offset);
    const chunkEnd = Math.min(file.size, chunkStart + length);
    console.debug(`Reading chunk from ${chunkStart} to ${chunkEnd} of file "${file.name}" - size ${file.size}`);

    const chunk = file.slice(chunkStart, chunkEnd);
    fileReader.readAsText(chunk);
  });

  return fileChunk;
}
