import { PropsWithChildren, PropsWithoutRef, ReactNode } from "react";

export enum TailwindColors {
  Stone = "stone",
  Red = "red",
  Orange = "orange",
  Amber = "amber",
  Yellow = "yellow",
  Lime = "lime",
  Green = "green",
  Emerald = "emerald",
  Teal = "teal",
  Cyan = "cyan",
  Sky = "sky",
  Blue = "blue",
  Indigo = "indigo",
  Violet = "violet",
  Purple = "purple",
  Fuchsia = "fuchsia",
  Pink = "pink",
  <PERSON> = "rose",
}

export enum AppSelectedColors {
  Primary = "primary",
  Secondary = "secondary",
  Red = "red",
  Orange = "orange",
  Yellow = "yellow",
  Green = "green",
  Emerald = "emerald",
  Cyan = "cyan",
  Blue = "blue",
  Indigo = "indigo",
  Purple = "purple",
  Rose = "rose",
}

export type AvailableColors = TailwindColors | AppSelectedColors;

export enum AvailableShades {
  DEFAULT = "DEFAULT",
  s50 = "50",
  s100 = "100",
  s200 = "200",
  s300 = "300",
  s400 = "400",
  s500 = "500",
  s600 = "600",
  s700 = "700",
  s800 = "800",
  s900 = "900",
  s950 = "950",
}

export interface IGenericComponentProps extends Omit<Partial<PropsWithoutRef<HTMLElement>>, "children"> {}

export type HTMLAnchorProps = Omit<Partial<PropsWithChildren<JSX.IntrinsicElements["a"]>>, "href"> & {
  href: string;
};

export type PagePropSearchParams = { [key: string]: string | string[] | undefined };
