export interface ParsedError {
  code: string;
  type: string;
  message: string;
  description: string;
  rawErrorObj: any;
  rawErrorStr: string;
  details: ParsedErrorDetail[];
}

export interface ParsedErrorDetail {
  msg: string;
  param?: string;
  desc?: string;
  // TODO parsed error description, field, more data
}

const msgKeys = ["message", "error", "error_message", "title", "status"];
const descKeys = [
  ...["description", "detail", "info", "type", "instance", "cause", "context", "category"],
  ...["fieldName", "fieldPath", "fieldValue", "source", "parameter", "code", "field"],
];
const objKeys = [
  ...["data", "error", "errors", "payload", "detail", "details", "error_detail", "error_details"],
  ...["metadata", "locations", "validation", "warning", "warnings", "fields"],
];

export default function parseError(unformattedError: any, alwaysReturn: boolean = true, depth: number = 0): ParsedError {
  const eErr: ParsedError = {
    code: "",
    type: "",
    message: "",
    description: "",
    rawErrorObj: unformattedError,
    rawErrorStr: "",
    details: [],
  };

  function pushMessage(msg: string, isPossiblyTitle: boolean = true) {
    if (!eErr.message && isPossiblyTitle) {
      eErr.message = msg;
    } else if (!eErr.description) {
      eErr.description = msg;
    } else {
      eErr.details.push({ msg: msg });
    }
  }

  try {
    try {
      eErr.rawErrorStr = JSON.stringify(unformattedError, null, 2);
    } catch (jsonstrErr) {
      try {
        eErr.rawErrorStr = Object.prototype.toString.call(unformattedError);
      } catch (objprotoErr) {
        try {
          eErr.rawErrorStr = String(unformattedError);
        } catch (caststrErr) {}
      }
    }

    try {
      if (typeof unformattedError === "string") {
        try {
          const wasJson = JSON.parse(unformattedError);
          if (typeof wasJson === "object") {
            return parseError(wasJson, alwaysReturn, depth + 1);
          }
        } catch (wasNotJson) {}

        eErr.message = unformattedError;
        return eErr;
      }

      if (unformattedError.code) eErr.code = unformattedError.code;
      if (unformattedError.type) eErr.type = unformattedError.type;

      for (let msgIdx = 0; msgIdx < msgKeys.length; msgIdx++) {
        const foundMsg = unformattedError[msgKeys[msgIdx]];
        if (typeof foundMsg === "string") {
          pushMessage(foundMsg);
        }
      }

      for (let descIdx = 0; descIdx < descKeys.length; descIdx++) {
        const foundDesc = unformattedError[descKeys[descIdx]];
        if (typeof foundDesc === "string") {
          pushMessage(foundDesc, false);
        }
      }

      for (let deepIdx = 0; deepIdx < descKeys.length; deepIdx++) {
        const objErr = unformattedError[objKeys[deepIdx]];

        if (typeof objErr === "string") {
          pushMessage(objErr);
          continue;
        }

        if (Array.isArray(objErr)) {
          objErr.forEach((errorItem) => {
            if (typeof errorItem === "string") {
              pushMessage(errorItem, false);
              return;
            }

            // recursive, go
            const emergedErr = parseError(errorItem, false, depth + 1);

            if (emergedErr) {
              pushMessage(emergedErr.message);
              if (emergedErr.description) {
                pushMessage(emergedErr.description, false);
              }
              eErr.details.push(...emergedErr.details);
              if (!eErr.code && emergedErr.code) eErr.code = emergedErr.code;
              if (!eErr.type && emergedErr.type) eErr.type = emergedErr.type;
            }
          });
        }

        const errorEntries = Object.entries(objErr);
        if (errorEntries.length == 0) {
          continue;
        }

        errorEntries.forEach(([key, errorItem]) => {
          if (typeof errorItem === "string") {
            if (msgKeys.includes(key)) {
              pushMessage(errorItem);
              return;
            }
            if (descKeys.includes(key)) {
              pushMessage(errorItem, false);
              return;
            }

            eErr.details.push({ param: key, msg: errorItem });
            return;
          }

          // recursive, go
          const emergedErr = parseError(errorItem, false, depth + 1);

          if (emergedErr) {
            pushMessage(emergedErr.message);
            if (emergedErr.description) {
              pushMessage(emergedErr.description, false);
            }
            eErr.details.push(...emergedErr.details);
            if (!eErr.code && emergedErr.code) eErr.code = emergedErr.code;
            if (!eErr.type && emergedErr.type) eErr.type = emergedErr.type;
          }
        });
      }

      if (!eErr.message && eErr.description) {
        eErr.message = eErr.description;
        eErr.description = "";
      }
    } catch (err) {}
  } catch (globalError) {}

  if (!eErr.message) {
    if (alwaysReturn) {
      eErr.message = "Unknown Error";
    } else {
      return null as any;
    }
  }

  return eErr;
}

/**
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
/* Examples:               * /

const examples = [
  {
    error: "Wrong URL format",
    status: "error",
    priority: 1,
    cost: 0,
  },

  {
    status: "fail",
    data: {
      error_message: "...",
    },
  },

  {
    error: {
      message: "...",
      name: '"HTTPError" | "ValidationError" | "WebsocketError" | "Error"',
    },
  },

  {
    errors: ["Repository url can't be blank"],
  },

  {
    error: "string",
    error_code: 400,
    error_description: "string",
  },

  {
    detail: "<string of error message>",
  },

  "Plain Error here",

  {
    status: 403,
    error: "Invalid number of results",
    detail: "You have requested too many results. Please refine your parameters.",
  },

  {
    id: "string",
    error: "string",
    metadata: {
      additionalProp1: "string",
      additionalProp2: "string",
      additionalProp3: "string",
    },
  },

  {
    errors: [
      {
        detail: "Daily rate limit of 1000 requests exceeded for this endpoint. See headers for additional details.",
        status: 429,
      },
    ],
  },

  {
    errors: [
      {
        detail: "You can only report the same IP address (`*********`) once in 15 minutes.",
        status: 429,
        source: {
          parameter: "ip",
        },
      },
    ],
  },

  {
    error: "invalid_request",
    error_description: "<reason_why>",
  },

  {
    data: null,
    errors: [
      {
        message: "validation",
        status: 400,
        locations: [
          {
            line: 2,
            column: 3,
          },
        ],
        validation: {
          id: ["The selected id is invalid."],
          score: ["The score may not be greater than 100."],
        },
      },
    ],
  },

  {
    data: null,
    errors: [
      {
        message: 'Cannot query field "nonexistentField" on type "MediaList".',
        status: 400,
        locations: [
          {
            line: 4,
            column: 5,
          },
        ],
      },
    ],
  },

  {
    success: false,
    error: {
      code: 104,
      info: "Your monthly API request volume has been reached. Please upgrade your plan.",
    },
  },

  {
    success: false,
    error: {
      code: 210,
      type: "no_phone_number_provided",
      info: "Please specify a phone number. [Example: 14158586273]",
    },
  },

  {
    status: 400,
    results: null,
    warnings: {
      0: {
        // Build a warning message here, sample text to show concept
        code: 1, // Specific field validation error code
        message: "It is no longer neccessary to put .js on the URL",
      },
    },
    errors: {
      0: {
        code: 2, // Specific field validation error code
        message: 'Field "x" is missing from the array structure',
        developer_message: "The request structure must contain the following fields {a,b,c{x,y,z}}",
      },
      1: {
        code: 3, // Specific field validation error code
        message: 'Incorrect Format for field "y"',
        developer_message: 'The field "y" must be in the form of "Y-m-d"',
      },
    },
  },

  {
    status: "fail",
    data: { title: "A title is required" },
  },

  {
    error: {
      message:
        "(#604) Your statement is not indexable. The WHERE clause must contain an indexable column. Such columns are marked with * in the tables linked from http://developers.facebook.com/docs/reference/fql ",
      type: "OAuthException",
      code: 604,
    },
  },

  {
    error: {
      message: "(#200) Must have a valid access_token to access this endpoint",
      type: "OAuthException",
      code: 200,
    },
  },

  {
    error: {
      message: "Message describing the error",
      type: "OAuthException",
      code: 190,
      error_subcode: 460,
      error_user_title: "A title",
      error_user_msg: "A message",
    },
  },

  {
    type: "https://example.com/probs/out-of-credit",
    title: "You do not have enough credit.",
    detail: "Your current balance is 30, but that costs 50.",
    instance: "/account/12345/msgs/abc",
    balance: 30,
    accounts: ["/account/12345", "/account/67890"],
  },

  {
    status: 0,
    message: "string",
    cause: "string",
    fields: {
      property1: ["string"],
      property2: ["string"],
    },
    context: "string",
  },

  {
    status: 400,
    message: "Invalid input data",
    cause: "The 'email' field is required.",
    fields: {
      "data.attributes.email": ["Email is required."],
    },
    context: "User registration",
  },

  {
    errors: [
      {
        category: "BAD_REQUEST",
        type: "REQUIRED_FIELD_MISSING",
        description: "must not be null",
        fieldName: "toAirportCode",
        fieldPath: "ShoppingRequest.oneWay",
      },
      {
        category: "BAD_REQUEST",
        type: "INVALID_VALUE",
        description: 'must match "^[A-Z]{3}$"',
        fieldName: "fromAirportCode",
        fieldPath: "ShoppingRequest.oneWay",
        fieldValue: "Dallas",
      },
    ],
  },

  {
    type: " a URL to a document describing the error condition (optional, and 'about:blank' is assumed if none is provided; should resolve to a human-readable document; Apigility always provides this).",
    title:
      " a brief title for the error condition (required; and should be the same for every problem of the same type; Apigility always provides this).",
    status: " the HTTP status code for the current request (optional; Apigility always provides this).",
    detail: " error details specific to this request (optional; Apigility requires it for each problem).",
    instance: " URI identifying the specific instance of this problem (optional; Apigility currently does not provide this).",
  },

  {
    errors: [
      {
        status: "400",
        source: { parameter: "bid" },
        detail: "Cannot accept both 'bid' and 'fold' parameters.",
      },
      {
        status: "400",
        source: { parameter: "fold" },
        detail: "Cannot accept both 'bid' and 'fold' parameters.",
      },
    ],
  },

  {
    code: 1010,
    status: false,
    errors: [
      {
        code: 1000,
        name: "Username invalid",
      },
      {
        code: 1001,
        name: "Password invalid",
      },
      {
        code: 1002,
        name: "Password not strong",
      },
    ],
    serverTime: "2013-12-03 12:34:02",
  },

  {
    status: 400,
    error: {
      code: 1, //General bad Request code
      message: "Bad Request",
      developer_message: "Field validation errors.",
      more_info: "www.api.com/help/errors/1",
      error_details: {
        0: {
          code: 2, // Specific field validation error code
          message: 'Field "x" is missing from the array structure',
          developer_message: "The request structure must contain the following fields {a,b,c{x,y,z}}",
          more_info: "www.api.com/help/errors/2",
        },

        1: {
          code: 3, // Specific field validation error code
          message: 'Incorrect Format for field "y"',
          developer_message: 'The field "y" must be in the form of "Y-m-d"',
          more_info: "www.api.com/help/errors/3",
        },
      },
    },
  },

  {
    error: {
      message: "(#200) Must have a valid access_token to access this endpoint",
      type: "OAuthException",
      code: 200,
    },
  },

  {
    status: 400,
    error: {
      code: 1, // General bad request code
      message: ['The Key "a" is missing', 'The Key "b" is missing', 'The Key "c" is missing', 'Incorrect Format for field "y"'],
    },
  },

  {
    status: "<HTTP STATUS CODE>",
    errors: [{ message: "Username is not set." }, { message: "Can't access the database." }],
  },

  {
    message: "Validation errors in your request", //  skip or optional error message
    errors: [
      {
        message: "Oops! The format is not correct",
        code: 35,
        field: "phoneNumber",
      },
    ],
  },

  {
    message: "Validation errors in your request", //  skip or optional error message
    errors: {
      email: ["Oops! The email is invalid"],
      phoneNumber: ["Oops! The phone number format is not correct"],
    },
  },

  {
    message: "Validation errors in your request", //  skip or optional error message
    errors: {
      email: [
        {
          message: "Oops! The email is invalid",
          code: 35,
        },
      ],
      phoneNumber: [
        {
          message: "Oops! The phone number format is not correct",
          code: 36,
        },
      ],
    },
  },
];

/* */
