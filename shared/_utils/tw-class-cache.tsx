const cache = (str?: any) => {};

// TODO: delete this
// FIXME: delete this

const makeColorsCache = (colors: string[], shades: string[]) => colors.map((c) => shades.map((s) => `bg-${c}-${s}`).join(" "));
cache([
  "bg-stone-50 bg-stone-100 bg-stone-200 bg-stone-300 bg-stone-400 bg-stone-500 bg-stone-600 bg-stone-700 bg-stone-800 bg-stone-900 bg-stone-950",
  "bg-red-50 bg-red-100 bg-red-200 bg-red-300 bg-red-400 bg-red-500 bg-red-600 bg-red-700 bg-red-800 bg-red-900 bg-red-950",
  "bg-orange-50 bg-orange-100 bg-orange-200 bg-orange-300 bg-orange-400 bg-orange-500 bg-orange-600 bg-orange-700 bg-orange-800 bg-orange-900 bg-orange-950",
  "bg-amber-50 bg-amber-100 bg-amber-200 bg-amber-300 bg-amber-400 bg-amber-500 bg-amber-600 bg-amber-700 bg-amber-800 bg-amber-900 bg-amber-950",
  "bg-yellow-50 bg-yellow-100 bg-yellow-200 bg-yellow-300 bg-yellow-400 bg-yellow-500 bg-yellow-600 bg-yellow-700 bg-yellow-800 bg-yellow-900 bg-yellow-950",
  "bg-lime-50 bg-lime-100 bg-lime-200 bg-lime-300 bg-lime-400 bg-lime-500 bg-lime-600 bg-lime-700 bg-lime-800 bg-lime-900 bg-lime-950",
  "bg-green-50 bg-green-100 bg-green-200 bg-green-300 bg-green-400 bg-green-500 bg-green-600 bg-green-700 bg-green-800 bg-green-900 bg-green-950",
  "bg-emerald-50 bg-emerald-100 bg-emerald-200 bg-emerald-300 bg-emerald-400 bg-emerald-500 bg-emerald-600 bg-emerald-700 bg-emerald-800 bg-emerald-900 bg-emerald-950",
  "bg-teal-50 bg-teal-100 bg-teal-200 bg-teal-300 bg-teal-400 bg-teal-500 bg-teal-600 bg-teal-700 bg-teal-800 bg-teal-900 bg-teal-950",
  "bg-cyan-50 bg-cyan-100 bg-cyan-200 bg-cyan-300 bg-cyan-400 bg-cyan-500 bg-cyan-600 bg-cyan-700 bg-cyan-800 bg-cyan-900 bg-cyan-950",
  "bg-sky-50 bg-sky-100 bg-sky-200 bg-sky-300 bg-sky-400 bg-sky-500 bg-sky-600 bg-sky-700 bg-sky-800 bg-sky-900 bg-sky-950",
  "bg-blue-50 bg-blue-100 bg-blue-200 bg-blue-300 bg-blue-400 bg-blue-500 bg-blue-600 bg-blue-700 bg-blue-800 bg-blue-900 bg-blue-950",
  "bg-indigo-50 bg-indigo-100 bg-indigo-200 bg-indigo-300 bg-indigo-400 bg-indigo-500 bg-indigo-600 bg-indigo-700 bg-indigo-800 bg-indigo-900 bg-indigo-950",
  "bg-violet-50 bg-violet-100 bg-violet-200 bg-violet-300 bg-violet-400 bg-violet-500 bg-violet-600 bg-violet-700 bg-violet-800 bg-violet-900 bg-violet-950",
  "bg-purple-50 bg-purple-100 bg-purple-200 bg-purple-300 bg-purple-400 bg-purple-500 bg-purple-600 bg-purple-700 bg-purple-800 bg-purple-900 bg-purple-950",
  "bg-fuchsia-50 bg-fuchsia-100 bg-fuchsia-200 bg-fuchsia-300 bg-fuchsia-400 bg-fuchsia-500 bg-fuchsia-600 bg-fuchsia-700 bg-fuchsia-800 bg-fuchsia-900 bg-fuchsia-950",
  "bg-pink-50 bg-pink-100 bg-pink-200 bg-pink-300 bg-pink-400 bg-pink-500 bg-pink-600 bg-pink-700 bg-pink-800 bg-pink-900 bg-pink-950",
  "bg-rose-50 bg-rose-100 bg-rose-200 bg-rose-300 bg-rose-400 bg-rose-500 bg-rose-600 bg-rose-700 bg-rose-800 bg-rose-950",
  "bg-primary-50 bg-primary-100 bg-primary-200 bg-primary-300 bg-primary-400 bg-primary-500 bg-primary-600 bg-primary-700 bg-primary-800 bg-primary-900 bg-primary-950",
  "bg-secondary-50 bg-secondary-100 bg-secondary-200 bg-secondary-300 bg-secondary-400 bg-secondary-500 bg-secondary-600 bg-secondary-700 bg-secondary-800 bg-secondary-900 bg-secondary-950",
]);

cache("3xl:ring-4 3xl:ring-2 3xl:ring-1 3xl:ring-0  |  3xl:ring-offset-4 3xl:ring-offset-2 3xl:ring-offset-1  |  3xl:ring-transparent");
cache("2xl:ring-4 2xl:ring-2 2xl:ring-1 2xl:ring-0  |  2xl:ring-offset-4 2xl:ring-offset-2 2xl:ring-offset-1  |  2xl:ring-transparent");
cache(" xl:ring-4  xl:ring-2  xl:ring-1  xl:ring-0  |   xl:ring-offset-4  xl:ring-offset-2  xl:ring-offset-1  |   xl:ring-transparent");
cache(" lg:ring-4  lg:ring-2  lg:ring-1  lg:ring-0  |   lg:ring-offset-4  lg:ring-offset-2  lg:ring-offset-1  |   lg:ring-transparent");
cache(" md:ring-4  md:ring-2  md:ring-1  md:ring-0  |   md:ring-offset-4  md:ring-offset-2  md:ring-offset-1  |   md:ring-transparent");
cache(" sm:ring-4  sm:ring-2  sm:ring-1  sm:ring-0  |   sm:ring-offset-4  sm:ring-offset-2  sm:ring-offset-1  |   sm:ring-transparent");


cache("3xl:ring-primary-700 3xl:ring-primary-600 3xl:ring-primary-500 3xl:ring-primary-400 3xl:ring-primary-300   |   3xl:ring-offset-primary-700 3xl:ring-offset-primary-600 3xl:ring-offset-primary-500 3xl:ring-offset-primary-400 3xl:ring-offset-primary-300");
cache("2xl:ring-primary-700 2xl:ring-primary-600 2xl:ring-primary-500 2xl:ring-primary-400 2xl:ring-primary-300   |   2xl:ring-offset-primary-700 2xl:ring-offset-primary-600 2xl:ring-offset-primary-500 2xl:ring-offset-primary-400 2xl:ring-offset-primary-300");
cache(" xl:ring-primary-700  xl:ring-primary-600  xl:ring-primary-500  xl:ring-primary-400  xl:ring-primary-300   |    xl:ring-offset-primary-700  xl:ring-offset-primary-600  xl:ring-offset-primary-500  xl:ring-offset-primary-400  xl:ring-offset-primary-300");
cache(" lg:ring-primary-700  lg:ring-primary-600  lg:ring-primary-500  lg:ring-primary-400  lg:ring-primary-300   |    lg:ring-offset-primary-700  lg:ring-offset-primary-600  lg:ring-offset-primary-500  lg:ring-offset-primary-400  lg:ring-offset-primary-300");
cache(" md:ring-primary-700  md:ring-primary-600  md:ring-primary-500  md:ring-primary-400  md:ring-primary-300   |    md:ring-offset-primary-700  md:ring-offset-primary-600  md:ring-offset-primary-500  md:ring-offset-primary-400  md:ring-offset-primary-300");
cache(" sm:ring-primary-700  sm:ring-primary-600  sm:ring-primary-500  sm:ring-primary-400  sm:ring-primary-300   |    sm:ring-offset-primary-700  sm:ring-offset-primary-600  sm:ring-offset-primary-500  sm:ring-offset-primary-400  sm:ring-offset-primary-300");

cache("3xl:ring-secondary-700 3xl:ring-secondary-600 3xl:ring-secondary-500 3xl:ring-secondary-400 3xl:ring-secondary-300   |   3xl:ring-offset-secondary-700 3xl:ring-offset-secondary-600 3xl:ring-offset-secondary-500 3xl:ring-offset-secondary-400 3xl:ring-offset-secondary-300");
cache("2xl:ring-secondary-700 2xl:ring-secondary-600 2xl:ring-secondary-500 2xl:ring-secondary-400 2xl:ring-secondary-300   |   2xl:ring-offset-secondary-700 2xl:ring-offset-secondary-600 2xl:ring-offset-secondary-500 2xl:ring-offset-secondary-400 2xl:ring-offset-secondary-300");
cache(" xl:ring-secondary-700  xl:ring-secondary-600  xl:ring-secondary-500  xl:ring-secondary-400  xl:ring-secondary-300   |    xl:ring-offset-secondary-700  xl:ring-offset-secondary-600  xl:ring-offset-secondary-500  xl:ring-offset-secondary-400  xl:ring-offset-secondary-300");
cache(" lg:ring-secondary-700  lg:ring-secondary-600  lg:ring-secondary-500  lg:ring-secondary-400  lg:ring-secondary-300   |    lg:ring-offset-secondary-700  lg:ring-offset-secondary-600  lg:ring-offset-secondary-500  lg:ring-offset-secondary-400  lg:ring-offset-secondary-300");
cache(" md:ring-secondary-700  md:ring-secondary-600  md:ring-secondary-500  md:ring-secondary-400  md:ring-secondary-300   |    md:ring-offset-secondary-700  md:ring-offset-secondary-600  md:ring-offset-secondary-500  md:ring-offset-secondary-400  md:ring-offset-secondary-300");
cache(" sm:ring-secondary-700  sm:ring-secondary-600  sm:ring-secondary-500  sm:ring-secondary-400  sm:ring-secondary-300   |    sm:ring-offset-secondary-700  sm:ring-offset-secondary-600  sm:ring-offset-secondary-500  sm:ring-offset-secondary-400  sm:ring-offset-secondary-300");

cache("3xl:ring-green-700 3xl:ring-green-600 3xl:ring-green-500 3xl:ring-green-400 3xl:ring-green-300   |   3xl:ring-offset-green-700 3xl:ring-offset-green-600 3xl:ring-offset-green-500 3xl:ring-offset-green-400 3xl:ring-offset-green-300");
cache("2xl:ring-green-700 2xl:ring-green-600 2xl:ring-green-500 2xl:ring-green-400 2xl:ring-green-300   |   2xl:ring-offset-green-700 2xl:ring-offset-green-600 2xl:ring-offset-green-500 2xl:ring-offset-green-400 2xl:ring-offset-green-300");
cache(" xl:ring-green-700  xl:ring-green-600  xl:ring-green-500  xl:ring-green-400  xl:ring-green-300   |    xl:ring-offset-green-700  xl:ring-offset-green-600  xl:ring-offset-green-500  xl:ring-offset-green-400  xl:ring-offset-green-300");
cache(" lg:ring-green-700  lg:ring-green-600  lg:ring-green-500  lg:ring-green-400  lg:ring-green-300   |    lg:ring-offset-green-700  lg:ring-offset-green-600  lg:ring-offset-green-500  lg:ring-offset-green-400  lg:ring-offset-green-300");
cache(" md:ring-green-700  md:ring-green-600  md:ring-green-500  md:ring-green-400  md:ring-green-300   |    md:ring-offset-green-700  md:ring-offset-green-600  md:ring-offset-green-500  md:ring-offset-green-400  md:ring-offset-green-300");
cache(" sm:ring-green-700  sm:ring-green-600  sm:ring-green-500  sm:ring-green-400  sm:ring-green-300   |    sm:ring-offset-green-700  sm:ring-offset-green-600  sm:ring-offset-green-500  sm:ring-offset-green-400  sm:ring-offset-green-300");

cache("3xl:ring-red-700 3xl:ring-red-600 3xl:ring-red-500 3xl:ring-red-400 3xl:ring-red-300   |   3xl:ring-offset-red-700 3xl:ring-offset-red-600 3xl:ring-offset-red-500 3xl:ring-offset-red-400 3xl:ring-offset-red-300");
cache("2xl:ring-red-700 2xl:ring-red-600 2xl:ring-red-500 2xl:ring-red-400 2xl:ring-red-300   |   2xl:ring-offset-red-700 2xl:ring-offset-red-600 2xl:ring-offset-red-500 2xl:ring-offset-red-400 2xl:ring-offset-red-300");
cache(" xl:ring-red-700  xl:ring-red-600  xl:ring-red-500  xl:ring-red-400  xl:ring-red-300   |    xl:ring-offset-red-700  xl:ring-offset-red-600  xl:ring-offset-red-500  xl:ring-offset-red-400  xl:ring-offset-red-300");
cache(" lg:ring-red-700  lg:ring-red-600  lg:ring-red-500  lg:ring-red-400  lg:ring-red-300   |    lg:ring-offset-red-700  lg:ring-offset-red-600  lg:ring-offset-red-500  lg:ring-offset-red-400  lg:ring-offset-red-300");
cache(" md:ring-red-700  md:ring-red-600  md:ring-red-500  md:ring-red-400  md:ring-red-300   |    md:ring-offset-red-700  md:ring-offset-red-600  md:ring-offset-red-500  md:ring-offset-red-400  md:ring-offset-red-300");
cache(" sm:ring-red-700  sm:ring-red-600  sm:ring-red-500  sm:ring-red-400  sm:ring-red-300   |    sm:ring-offset-red-700  sm:ring-offset-red-600  sm:ring-offset-red-500  sm:ring-offset-red-400  sm:ring-offset-red-300");

cache("3xl:ring-yellow-700 3xl:ring-yellow-600 3xl:ring-yellow-500 3xl:ring-yellow-400 3xl:ring-yellow-300   |   3xl:ring-offset-yellow-700 3xl:ring-offset-yellow-600 3xl:ring-offset-yellow-500 3xl:ring-offset-yellow-400 3xl:ring-offset-yellow-300");
cache("2xl:ring-yellow-700 2xl:ring-yellow-600 2xl:ring-yellow-500 2xl:ring-yellow-400 2xl:ring-yellow-300   |   2xl:ring-offset-yellow-700 2xl:ring-offset-yellow-600 2xl:ring-offset-yellow-500 2xl:ring-offset-yellow-400 2xl:ring-offset-yellow-300");
cache(" xl:ring-yellow-700  xl:ring-yellow-600  xl:ring-yellow-500  xl:ring-yellow-400  xl:ring-yellow-300   |    xl:ring-offset-yellow-700  xl:ring-offset-yellow-600  xl:ring-offset-yellow-500  xl:ring-offset-yellow-400  xl:ring-offset-yellow-300");
cache(" lg:ring-yellow-700  lg:ring-yellow-600  lg:ring-yellow-500  lg:ring-yellow-400  lg:ring-yellow-300   |    lg:ring-offset-yellow-700  lg:ring-offset-yellow-600  lg:ring-offset-yellow-500  lg:ring-offset-yellow-400  lg:ring-offset-yellow-300");
cache(" md:ring-yellow-700  md:ring-yellow-600  md:ring-yellow-500  md:ring-yellow-400  md:ring-yellow-300   |    md:ring-offset-yellow-700  md:ring-offset-yellow-600  md:ring-offset-yellow-500  md:ring-offset-yellow-400  md:ring-offset-yellow-300");
cache(" sm:ring-yellow-700  sm:ring-yellow-600  sm:ring-yellow-500  sm:ring-yellow-400  sm:ring-yellow-300   |    sm:ring-offset-yellow-700  sm:ring-offset-yellow-600  sm:ring-offset-yellow-500  sm:ring-offset-yellow-400  sm:ring-offset-yellow-300");

cache("3xl:ring-blue-700 3xl:ring-blue-600 3xl:ring-blue-500 3xl:ring-blue-400 3xl:ring-blue-300   |   3xl:ring-offset-blue-700 3xl:ring-offset-blue-600 3xl:ring-offset-blue-500 3xl:ring-offset-blue-400 3xl:ring-offset-blue-300");
cache("2xl:ring-blue-700 2xl:ring-blue-600 2xl:ring-blue-500 2xl:ring-blue-400 2xl:ring-blue-300   |   2xl:ring-offset-blue-700 2xl:ring-offset-blue-600 2xl:ring-offset-blue-500 2xl:ring-offset-blue-400 2xl:ring-offset-blue-300");
cache(" xl:ring-blue-700  xl:ring-blue-600  xl:ring-blue-500  xl:ring-blue-400  xl:ring-blue-300   |    xl:ring-offset-blue-700  xl:ring-offset-blue-600  xl:ring-offset-blue-500  xl:ring-offset-blue-400  xl:ring-offset-blue-300");
cache(" lg:ring-blue-700  lg:ring-blue-600  lg:ring-blue-500  lg:ring-blue-400  lg:ring-blue-300   |    lg:ring-offset-blue-700  lg:ring-offset-blue-600  lg:ring-offset-blue-500  lg:ring-offset-blue-400  lg:ring-offset-blue-300");
cache(" md:ring-blue-700  md:ring-blue-600  md:ring-blue-500  md:ring-blue-400  md:ring-blue-300   |    md:ring-offset-blue-700  md:ring-offset-blue-600  md:ring-offset-blue-500  md:ring-offset-blue-400  md:ring-offset-blue-300");
cache(" sm:ring-blue-700  sm:ring-blue-600  sm:ring-blue-500  sm:ring-blue-400  sm:ring-blue-300   |    sm:ring-offset-blue-700  sm:ring-offset-blue-600  sm:ring-offset-blue-500  sm:ring-offset-blue-400  sm:ring-offset-blue-300");


cache("3xl:bg-primary-700 3xl:bg-primary-600 3xl:bg-primary-500 3xl:bg-primary-400 3xl:bg-primary-300   |   3xl:border-primary-700 3xl:border-primary-600 3xl:border-primary-500 3xl:border-primary-400 3xl:border-primary-300");
cache("2xl:bg-primary-700 2xl:bg-primary-600 2xl:bg-primary-500 2xl:bg-primary-400 2xl:bg-primary-300   |   2xl:border-primary-700 2xl:border-primary-600 2xl:border-primary-500 2xl:border-primary-400 2xl:border-primary-300");
cache(" xl:bg-primary-700  xl:bg-primary-600  xl:bg-primary-500  xl:bg-primary-400  xl:bg-primary-300   |    xl:border-primary-700  xl:border-primary-600  xl:border-primary-500  xl:border-primary-400  xl:border-primary-300");
cache(" lg:bg-primary-700  lg:bg-primary-600  lg:bg-primary-500  lg:bg-primary-400  lg:bg-primary-300   |    lg:border-primary-700  lg:border-primary-600  lg:border-primary-500  lg:border-primary-400  lg:border-primary-300");
cache(" md:bg-primary-700  md:bg-primary-600  md:bg-primary-500  md:bg-primary-400  md:bg-primary-300   |    md:border-primary-700  md:border-primary-600  md:border-primary-500  md:border-primary-400  md:border-primary-300");
cache(" sm:bg-primary-700  sm:bg-primary-600  sm:bg-primary-500  sm:bg-primary-400  sm:bg-primary-300   |    sm:border-primary-700  sm:border-primary-600  sm:border-primary-500  sm:border-primary-400  sm:border-primary-300");

cache("3xl:bg-secondary-700 3xl:bg-secondary-600 3xl:bg-secondary-500 3xl:bg-secondary-400 3xl:bg-secondary-300   |   3xl:border-secondary-700 3xl:border-secondary-600 3xl:border-secondary-500 3xl:border-secondary-400 3xl:border-secondary-300");
cache("2xl:bg-secondary-700 2xl:bg-secondary-600 2xl:bg-secondary-500 2xl:bg-secondary-400 2xl:bg-secondary-300   |   2xl:border-secondary-700 2xl:border-secondary-600 2xl:border-secondary-500 2xl:border-secondary-400 2xl:border-secondary-300");
cache(" xl:bg-secondary-700  xl:bg-secondary-600  xl:bg-secondary-500  xl:bg-secondary-400  xl:bg-secondary-300   |    xl:border-secondary-700  xl:border-secondary-600  xl:border-secondary-500  xl:border-secondary-400  xl:border-secondary-300");
cache(" lg:bg-secondary-700  lg:bg-secondary-600  lg:bg-secondary-500  lg:bg-secondary-400  lg:bg-secondary-300   |    lg:border-secondary-700  lg:border-secondary-600  lg:border-secondary-500  lg:border-secondary-400  lg:border-secondary-300");
cache(" md:bg-secondary-700  md:bg-secondary-600  md:bg-secondary-500  md:bg-secondary-400  md:bg-secondary-300   |    md:border-secondary-700  md:border-secondary-600  md:border-secondary-500  md:border-secondary-400  md:border-secondary-300");
cache(" sm:bg-secondary-700  sm:bg-secondary-600  sm:bg-secondary-500  sm:bg-secondary-400  sm:bg-secondary-300   |    sm:border-secondary-700  sm:border-secondary-600  sm:border-secondary-500  sm:border-secondary-400  sm:border-secondary-300");

cache("3xl:bg-green-700 3xl:bg-green-600 3xl:bg-green-500 3xl:bg-green-400 3xl:bg-green-300   |   3xl:border-green-700 3xl:border-green-600 3xl:border-green-500 3xl:border-green-400 3xl:border-green-300");
cache("2xl:bg-green-700 2xl:bg-green-600 2xl:bg-green-500 2xl:bg-green-400 2xl:bg-green-300   |   2xl:border-green-700 2xl:border-green-600 2xl:border-green-500 2xl:border-green-400 2xl:border-green-300");
cache(" xl:bg-green-700  xl:bg-green-600  xl:bg-green-500  xl:bg-green-400  xl:bg-green-300   |    xl:border-green-700  xl:border-green-600  xl:border-green-500  xl:border-green-400  xl:border-green-300");
cache(" lg:bg-green-700  lg:bg-green-600  lg:bg-green-500  lg:bg-green-400  lg:bg-green-300   |    lg:border-green-700  lg:border-green-600  lg:border-green-500  lg:border-green-400  lg:border-green-300");
cache(" md:bg-green-700  md:bg-green-600  md:bg-green-500  md:bg-green-400  md:bg-green-300   |    md:border-green-700  md:border-green-600  md:border-green-500  md:border-green-400  md:border-green-300");
cache(" sm:bg-green-700  sm:bg-green-600  sm:bg-green-500  sm:bg-green-400  sm:bg-green-300   |    sm:border-green-700  sm:border-green-600  sm:border-green-500  sm:border-green-400  sm:border-green-300");

cache("3xl:bg-red-700 3xl:bg-red-600 3xl:bg-red-500 3xl:bg-red-400 3xl:bg-red-300   |   3xl:border-red-700 3xl:border-red-600 3xl:border-red-500 3xl:border-red-400 3xl:border-red-300");
cache("2xl:bg-red-700 2xl:bg-red-600 2xl:bg-red-500 2xl:bg-red-400 2xl:bg-red-300   |   2xl:border-red-700 2xl:border-red-600 2xl:border-red-500 2xl:border-red-400 2xl:border-red-300");
cache(" xl:bg-red-700  xl:bg-red-600  xl:bg-red-500  xl:bg-red-400  xl:bg-red-300   |    xl:border-red-700  xl:border-red-600  xl:border-red-500  xl:border-red-400  xl:border-red-300");
cache(" lg:bg-red-700  lg:bg-red-600  lg:bg-red-500  lg:bg-red-400  lg:bg-red-300   |    lg:border-red-700  lg:border-red-600  lg:border-red-500  lg:border-red-400  lg:border-red-300");
cache(" md:bg-red-700  md:bg-red-600  md:bg-red-500  md:bg-red-400  md:bg-red-300   |    md:border-red-700  md:border-red-600  md:border-red-500  md:border-red-400  md:border-red-300");
cache(" sm:bg-red-700  sm:bg-red-600  sm:bg-red-500  sm:bg-red-400  sm:bg-red-300   |    sm:border-red-700  sm:border-red-600  sm:border-red-500  sm:border-red-400  sm:border-red-300");

cache("3xl:bg-yellow-700 3xl:bg-yellow-600 3xl:bg-yellow-500 3xl:bg-yellow-400 3xl:bg-yellow-300   |   3xl:border-yellow-700 3xl:border-yellow-600 3xl:border-yellow-500 3xl:border-yellow-400 3xl:border-yellow-300");
cache("2xl:bg-yellow-700 2xl:bg-yellow-600 2xl:bg-yellow-500 2xl:bg-yellow-400 2xl:bg-yellow-300   |   2xl:border-yellow-700 2xl:border-yellow-600 2xl:border-yellow-500 2xl:border-yellow-400 2xl:border-yellow-300");
cache(" xl:bg-yellow-700  xl:bg-yellow-600  xl:bg-yellow-500  xl:bg-yellow-400  xl:bg-yellow-300   |    xl:border-yellow-700  xl:border-yellow-600  xl:border-yellow-500  xl:border-yellow-400  xl:border-yellow-300");
cache(" lg:bg-yellow-700  lg:bg-yellow-600  lg:bg-yellow-500  lg:bg-yellow-400  lg:bg-yellow-300   |    lg:border-yellow-700  lg:border-yellow-600  lg:border-yellow-500  lg:border-yellow-400  lg:border-yellow-300");
cache(" md:bg-yellow-700  md:bg-yellow-600  md:bg-yellow-500  md:bg-yellow-400  md:bg-yellow-300   |    md:border-yellow-700  md:border-yellow-600  md:border-yellow-500  md:border-yellow-400  md:border-yellow-300");
cache(" sm:bg-yellow-700  sm:bg-yellow-600  sm:bg-yellow-500  sm:bg-yellow-400  sm:bg-yellow-300   |    sm:border-yellow-700  sm:border-yellow-600  sm:border-yellow-500  sm:border-yellow-400  sm:border-yellow-300");

cache("3xl:bg-blue-700 3xl:bg-blue-600 3xl:bg-blue-500 3xl:bg-blue-400 3xl:bg-blue-300   |   3xl:border-blue-700 3xl:border-blue-600 3xl:border-blue-500 3xl:border-blue-400 3xl:border-blue-300");
cache("2xl:bg-blue-700 2xl:bg-blue-600 2xl:bg-blue-500 2xl:bg-blue-400 2xl:bg-blue-300   |   2xl:border-blue-700 2xl:border-blue-600 2xl:border-blue-500 2xl:border-blue-400 2xl:border-blue-300");
cache(" xl:bg-blue-700  xl:bg-blue-600  xl:bg-blue-500  xl:bg-blue-400  xl:bg-blue-300   |    xl:border-blue-700  xl:border-blue-600  xl:border-blue-500  xl:border-blue-400  xl:border-blue-300");
cache(" lg:bg-blue-700  lg:bg-blue-600  lg:bg-blue-500  lg:bg-blue-400  lg:bg-blue-300   |    lg:border-blue-700  lg:border-blue-600  lg:border-blue-500  lg:border-blue-400  lg:border-blue-300");
cache(" md:bg-blue-700  md:bg-blue-600  md:bg-blue-500  md:bg-blue-400  md:bg-blue-300   |    md:border-blue-700  md:border-blue-600  md:border-blue-500  md:border-blue-400  md:border-blue-300");
cache(" sm:bg-blue-700  sm:bg-blue-600  sm:bg-blue-500  sm:bg-blue-400  sm:bg-blue-300   |    sm:border-blue-700  sm:border-blue-600  sm:border-blue-500  sm:border-blue-400  sm:border-blue-300");

