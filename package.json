{"name": "digai-project-x", "version": "0.1.0", "private": true, "author": "Digaí", "type": "module", "homepage": "https://www.admin.collectcash.io/", "browserslist": ["defaults and fully supports es6-module"], "scripts": {"dev": "next dev -p 3390", "build": "next build", "start": "next start -p 3390", "lint": "next lint", "postcss": "sass ./public/assets/scss/style.scss ./public/assets/css/style.css && postcss ./public/assets/css/style.css -o ./public/assets/css/style.css", "sass": "sass ./public/assets/scss/:./public/assets/css/", "sass-min": "sass ./public/assets/scss/:./public/assets/css/ --style compressed", "dev:watch": "concurrently npm run dev"}, "dependencies": {"@headlessui/react": "2.2.0", "@reduxjs/toolkit": "^1.9.7", "@tanstack/react-query": "^5.59.9", "@types/react-datepicker": "^6.2.0", "antd": "5.22.6", "apexcharts": "^4.1.0", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "dayjs": "^1.11.13", "deepmerge": "^4.3.1", "lodash": "^4.17.21", "next": "^15.0.1", "next-auth": "^4.24.8", "next-intl": "^3.21.1", "preline": "^2.1.0", "rc-field-form": "^2.4.0", "react": "^18.3.1", "react-apexcharts": "^1.6.0", "react-datepicker": "^8.2.1", "react-dom": "^18.3.1", "react-google-recaptcha-v3": "^1.10.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.1.3", "react-select": "^5.8.3", "react-simple-maps": "^3.0.0", "react-tooltip": "^5.28.0", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "sass": "^1.80.6", "tailwind-clip-path": "^1.0.0", "tailwind-merge": "^2.5.4", "uuid": "^10.0.0", "zod": "^3.23.8", "zustand": "^5.0.0-rc.2"}, "optionalDependencies": {"@simonwep/pickr": "^1.9.1", "firebase": "^10.11.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "nookies": "^2.5.2", "papaparse": "^5.4.1", "react-spring": "^9.7.4", "simplebar-react": "^3.2.6", "socket.io-client": "^4.8.0", "styled-components": "^6.1.13", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.4.0-alpha.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/jsonwebtoken": "9.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-simple-maps": "^3.0.6", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwind-config-viewer": "^2.0.4", "tailwindcss": "^3.4.1", "typescript": "^5"}}