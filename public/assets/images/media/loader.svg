<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto;" width="80px" height="80px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
<g transform="rotate(0 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.5128205128205128s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(27.692307692307693 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.47008547008547s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(55.38461538461539 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.42735042735042733s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(83.07692307692308 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.3846153846153846s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(110.76923076923077 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.34188034188034183s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(138.46153846153845 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.29914529914529914s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(166.15384615384616 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.2564102564102564s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(193.84615384615384 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.21367521367521367s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(221.53846153846155 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.17094017094017092s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(249.23076923076923 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.1282051282051282s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(276.9230769230769 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.08547008547008546s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(304.61538461538464 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="-0.04273504273504273s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(332.3076923076923 50 50)">
  <rect x="47.5" y="21" rx="2.5" ry="6" width="5" height="12" fill="#845adf">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="0.5555555555555556s" begin="0s" repeatCount="indefinite"></animate>
  </rect>
</g>
<!-- [ldio] generated by https://loading.io/ --></svg>