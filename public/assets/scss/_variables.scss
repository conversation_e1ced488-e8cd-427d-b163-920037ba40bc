:root {

  // --primary-rgb:            132,  90, 223;  .as__primary-rgb {           gap: rgb( 132,   90,  223 ); }

  --primary:     132   90  223;         --c-000: rgb( 132,   90,  223 );
  --secondary:    35  183  229;         --c-001: rgb(  35,  183,  229 );

  --light:       243  246  248;         --c-002: rgb( 243,  246,  248 );
  --dark:         35   35   35;         --c-003: rgb(  35,   35,   35 );

  --warning:     245  184   73;         --c-004: rgb( 245,  184,   73 );
  --info:         73  182  245;         --c-005: rgb(  73,  182,  245 );
  --success:      38  191  148;         --c-006: rgb(  38,  191,  148 );
  --danger:      230   83   60;         --c-007: rgb( 230,   83,   60 );

  --pink:        231  145  188;         --c-008: rgb( 231,  145,  188 );
	--red:         208   61   70;         --c-009: rgb( 208,   61,   70 );
  --orange:      255  165    5;         --c-010: rgb( 255,  165,    5 );
  --yellow:      255  193    2;         --c-011: rgb( 255,  193,    2 );
  --green:        29  216  113;         --c-012: rgb(  29,  216,  113 );
  --teal:         18  194  194;         --c-013: rgb(  18,  194,  194 );
  --cyan:          0  209  209;         --c-014: rgb(   0,  209,  209 );
  --blue:         43   62  101;         --c-015: rgb(  43,   62,  101 );
  --purple:      137   32  173;         --c-016: rgb( 137,   32,  173 );
  --indigo:       77   93  219;         --c-017: rgb(  77,   93,  219 );
  --gray:        134  153  163;         --c-018: rgb( 134,  153,  163 );

  --body-bg:     240  241  247;         --c-019: rgb( 240,  241,  247 );
  --text-muted:  140  144  151;         --c-020: rgb( 140,  144,  151 );

  
  --facebook:     59   89  152;         --c-021: rgb(  59,   89,  152 );
  --twitter:       0  172  238;         --c-022: rgb(   0,  172,  238 );
  --github:       51   51   51;         --c-023: rgb(  51,   51,   51 );
  --google:      207   78   67;         --c-024: rgb( 207,   78,   67 );
  --youtube:     255    0    0;         --c-025: rgb( 255,    0,    0 );

  --default-background:     247  248  249;       --c-026: rgb( 247,  248,  249 );
  --default-text-color:      51   51   53;       --c-027: rgb(  51,   51,   53 );
  --default-border:         243  243  243;       --c-028: rgb( 243,  243,  243 );
  --input-border:           185  190  195;       --c-029: rgb( 185,  190,  195 );

  --menu-prime-color:        83  100  133;       --c-030: rgb(  83,  100,  133 );
  --menu-border-color:      243  243  243;       --c-031: rgb( 243,  243,  243 );
  --header-prime-color:      83  100  133;       --c-032: rgb(  83,  100,  133 );
  --header-border-color:    243  243  243;       --c-033: rgb( 243,  243,  243 );
  --bootstrap-card-border:  255    0    0;       --c-034: rgb( 255,    0,    0 );
  // --bootstrap-card-border:  243  243  243;       --c-034: rgb( 243,  243,  243 );
  --list-hover-focus-bg:    245  246  247;       --c-035: rgb( 245,  246,  247 );

  --bs-width: 1px;
}

/* dark mode */
.dark {
  --body-bg:                 26   28   30;
  --dark-bg:                 37   39   41;
  --light:                   43   46   49;
  --dark:                   240  245  248;
  --input-border:            49   51   53;
  --form-control-bg:         35   38   40;
  --default-text-color:     255  255  255;
}