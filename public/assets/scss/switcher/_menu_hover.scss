[data-nav-style="menu-hover"][data-nav-layout="horizontal"] {
    @extend .menu-hover;
    @media (min-width: 992px) {
        .app-sidebar {
            .side-menu__item {
                @apply flex items-center px-[1.6rem] #{!important};
                
            }
            .side-menu__icon {
                @apply mb-0 me-[0.35rem] #{!important};   
            }
            .side-menu__angle {
                @apply block #{!important};
                
            }
            .slide.has-sub .slide-menu {
                &.active {
                    @apply start-auto end-auto rounded-none #{!important};
                    
                }
                &.child1 {
                    @apply top-[100%] #{!important};
                   
                }
            }
        }
    }
}
[data-nav-style="menu-hover"][data-toggled="menu-hover-closed"] {
    @extend .menu-hover;
}
[data-nav-style="menu-hover"][data-nav-layout="vertical"][data-toggled="menu-hover-closed"] {
    @media (min-width: 992px) {
        .app-sidebar {
            @apply absolute #{!important} ;
           
            .side-menu__item {
                @apply rounded-md mt-1 mb-0 mx-1 #{!important} ;
                &:last-child {
                    @apply m-1 #{!important};
                    
                }
            }
        }
        .app-sidebar {
            .slide .slide-menu {
                &.child1,&.child2,&.child3 {
                    @apply rounded-e-md rounded-s-none;
                }
            } 
        }
    }
}
.menu-hover {
    @media (min-width: 992px) {
        .content{
            @apply ms-[9.5rem];
        }
        .app-sidebar {
            @apply w-[9.5rem];
        
            .main-sidebar {
                @apply overflow-visible h-[90%] ;
              
            }
            .main-sidebar-header {
                @apply w-[9.5rem];
            
            }
            .side-menu__icon {
                @apply me-0 mb-2;
                
            }
            .slide {
                @apply p-0;
                
            }
            .slide-menu {
                &.child1,
                &.child2,
                &.child3 {
                    @apply min-w-[12rem] hidden #{!important};
                    .slide {
                        .side-menu__item {
                            @apply text-start;
                        
                            &:before {
                                @apply top-[0.938rem] start-3;
                                
                            }
                        }
                    }
                    .slide.has-sub,.slide {
                        &.side-menu__label1 {
                            @apply hidden;
                            
                        }
                    }
                }
            }
            .slide.has-sub:hover {
                .slide-menu.child1 {
                    @apply block #{!important};
                
                    .slide.has-sub:hover {
                        .slide-menu.child2 {
                            @apply block #{!important};
                            
                            .slide.has-sub:hover {
                                .slide-menu.child3 {
                                    @apply block #{!important};
                                    
                                }
                            }
                        }
                    }
                }
            }
            .slide__category,
            .side-menu__angle {
                @apply hidden;
                
            }
            .slide-menu {
                &.child1,
                &.child2,
                &.child3 {
                    .side-menu__angle {
                        @apply block end-4 top-[0.65rem];
                        
                    }
                }
            }
            .side-menu__item,
            .side-menu__label {
                @apply block text-center;
                
            }
            .slide.has-sub .slide-menu {
                @apply absolute start-[9.5rem]   top-auto transition-none rounded-e-md shadow-[0.125rem_0.063rem_0.5rem_rgba(0,0,0,0.1)] dark:shadow-[0.125rem_0.063rem_0.5rem_rgba(255,255,255,0.1)] #{!important};
                
                &.child2,
                &.child3 {
                        @apply start-48 #{!important};
                    
                }
            }
            .simplebar-content-wrapper {
                position: initial;
            }
            .simplebar-mask {
                position: inherit;
            }
            .simplebar-placeholder {
                @apply h-auto #{!important};

            }
        }
        .app-header {
            @apply ps-[9.5rem];
            
        }
        .content  {
            @apply ms-[9.5rem];
        
        }
    }
}

@media (min-width: 992px) {
    [data-nav-style="menu-hover"][data-toggled="menu-hover-closed"]:not([data-nav-layout="horizontal"]) {
      .content {
        @apply min-h-[100rem];
      }
    }
  }