/* Start IconText-Menu Styles */
[data-vertical-style="icontext"][data-toggled="icon-text-close"] {
  @media screen and (min-width: 992px) {
    &:not([data-icon-text="open"]) {
      .app-sidebar {
        @apply w-[9.5rem] #{!important};
        .slide__category,
        .side-menu__angle {
          @apply hidden;
        }
        .side-menu__item {
          @apply block text-center text-xs px-0 #{!important};
          .side-menu__icon {
            @apply mx-auto mb-2 w-full block;
          }
        }
        .slide.has-sub {
          .slide-menu {
            @apply hidden px-0 #{!important};
          }
        }
        .app-sidebar .slide {
          @apply px-[0.25rem] #{!important};
        }
        .main-sidebar-header {
          @apply w-[9.5rem] #{!important};
          .header-logo {
            .desktop-logo,
            .toggle-logo,.toggle-white,
            .desktop-white {
              @apply hidden;
            }
            .desktop-dark {
              @apply block;
            }
            .toggle-dark {
              @apply hidden;
            }
          }
        }
        .main-menu {
          @apply px-0;
        }
      }
      &.dark {
        .app-sidebar {
          .main-sidebar-header {
            .header-logo {
              .toggle-logo,
              .toggle-dark ,.toggle-white{
                @apply hidden;
              }
              .desktop-dark {
                @apply block;
              }
            }
          }
        }
      }
    }
    .content {
      @apply ms-[9.5rem];
    }
    .app-header {
      @apply ps-[9.5rem];
    }
  }
}
/* End IconText-Menu Styles */
