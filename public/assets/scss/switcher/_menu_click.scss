[data-nav-style="menu-click"][data-nav-layout="horizontal"] {
  @extend .menu-click;
  @media (min-width: 992px) {
    .app-sidebar {
      .side-menu__item {
        @apply flex items-center px-[1.6rem] #{!important};
      }
      .side-menu__icon {
        @apply mb-0 me-[0.35rem] #{!important};
      }
      .side-menu__angle {
        @apply block #{!important};
      }
      .slide.has-sub .slide-menu {
        @apply start-auto #{!important};

        &.active {
          @apply start-auto end-auto #{!important};
        }
        &.child1 {
          @apply top-[100%] #{!important};
        }
      }
    }
    .app-header {
      @apply ps-[9.5rem];
    }
  }
}
[data-nav-style="menu-click"][data-toggled="menu-click-closed"] {
  @extend .menu-click;
  .app-header {
    @apply ps-[9.5rem];
  }
}
[data-nav-style="menu-click"][data-nav-layout="vertical"][data-toggled="menu-click-closed"] {
  @media (min-width: 992px) {
    .app-sidebar {
      @apply absolute #{!important};

      .side-menu__item {
        @apply rounded-md mt-1 mx-1 mb-0 #{!important};
        &:last-child {
          @apply m-1 #{!important};
        }
      }
    }
    .app-sidebar {
      .slide .slide-menu {
        &.child1,
        &.child2,
        &.child3 {
          @apply rounded-e-md #{!important};
        }
      }
    }
  }
}
.menu-click {
  @media (min-width: 992px) {
    .content {
      @apply ms-[9.5rem];
    }
    .app-sidebar {
      @apply w-[9.5rem];

      .main-sidebar {
        @apply overflow-visible h-[90%];
      }
      .main-sidebar-header {
        @apply w-[9.5rem];
      }
      .side-menu__icon {
        @apply me-0 mb-2;
      }
      .slide {
        @apply p-0;
      }
      .slide-menu {
        &.child1,
        &.child2,
        &.child3 {
          @apply min-w-[12rem];

          .slide {
            .side-menu__item {
              @apply text-start;

              &:before {
                @apply top-[0.938rem] start-3;
              }
            }
          }
          .side-menu__angle {
            @apply block end-4 top-[0.65rem];
          }
          .slide.has-sub,
          .slide {
            &.side-menu__label1 {
              @apply hidden;
            }
          }
        }
      }
      .slide__category,
      .side-menu__angle {
        @apply hidden;
      }
      .side-menu__item,
      .side-menu__label {
        @apply block text-center;
      }
      .slide.has-sub .slide-menu {
        @apply absolute start-[9.5rem]  top-auto  transition-none shadow-[0.125rem_0.063rem_0.5rem_rgba(0,0,0,0.1)] dark:shadow-[0.125rem_0.063rem_0.5rem_rgba(255,255,255,0.1)] #{!important};

        &.child2,
        &.child3 {
          @apply start-48 #{!important};
        }
      }
      .simplebar-content-wrapper {
        position: initial;
      }
      .simplebar-mask {
        position: inherit;
      }
      .simplebar-placeholder {
        @apply h-auto #{!important};
      }
    }
    .content {
      @apply ms-[9.5rem];
    }
  }
  @media (max-width: 991.98px) {
    .app-sidebar {
      @apply w-60;
    }
  }
}
@media (min-width: 992px) {
  [data-nav-style="menu-click"][data-toggled="menu-click-closed"]:not([data-nav-layout="horizontal"]) {
    .content {
      @apply min-h-[100rem];
    }
  }
}
