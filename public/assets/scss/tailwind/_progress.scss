.progress {
  @apply bg-light h-[0.75rem] rounded-sm flex w-full;
}
/* Start:: progressbar-height */
.progress,
.progress-stacked {
  &.progress-xs {
    @apply h-[0.3125rem];
  }
  &.progress-sm {
    @apply h-[0.5rem];
  }
  &.progress-lg {
    @apply h-[1rem];
  }
  &.progress-xl {
    @apply h-[1.25rem];
  }
}
/* End:: progressbar-height */
.progress-stacked {
  @apply bg-light;
}
.progress-bar {
  @apply text-white bg-primary flex justify-center items-center rounded-s-full;
}

// @keyframes progress-animate {
//   0% {
//     @apply w-0;
//   }
// }

.progress-animate {
  @apply relative rounded-[0.625rem];
  .progress-bar {
    @apply relative rounded-[0.625rem];
  }
}

/* Start:: custom-progress */
.progress-custom {
  @apply overflow-visible relative;
  .progress-bar {
    @apply relative overflow-visible after:w-[1.15rem] after:h-[1.15rem] after:rounded-[50%] after:bg-white after:absolute after:-end-[0.375rem]
        after:-top-[0.375rem] after:border-[0.25rem] after:border-solid after:border-primary;
    &.progress-secondary {
      @apply bg-secondary after:border-secondary #{!important};
      .progress-bar-value {
        @apply after:border-t-secondary;
      }
    }
    &.progress-warning {
      @apply bg-warning after:border-warning #{!important};
      .progress-bar-value {
        @apply after:border-t-warning;
      }
    }
    &.progress-info {
      @apply bg-info after:border-info #{!important};
      .progress-bar-value {
        @apply after:border-t-info;
      }
    }
    &.progress-success {
      @apply bg-success after:border-success #{!important};
      .progress-bar-value {
        @apply after:border-t-success;
      }
    }
    &.progress-danger {
      @apply bg-danger after:border-danger #{!important};
      .progress-bar-value {
        @apply after:border-t-danger;
      }
    }
  }
  .progress-bar-value {
    @apply w-[1.875rem] h-[1.25rem] leading-[1.25rem] rounded-[.188rem] bg-primary shadow-[0_0.313rem_0.313rem_rgba(0,0,0,.4)] dark:shadow-[rgba(255,255,255,.4)] text-[.625rem] font-semibold text-white text-center absolute bottom-6 end-[-0.688rem]
    after:absolute after:border-x-[0.438rem] after:border-x-transparent after:border-t-[0.438rem] after:border-primary after:bottom-[-0.375rem] after:start-[28%];
  }
}
.progress-bar-title {
  @apply py-[0.313rem] px-[0.625rem] m-0 bg-primary rounded-s-sm text-[0.625rem] font-semibold text-white uppercase absolute -top-[0.438rem] start-0 z-[1]
    after:absolute  after:top-0 after:-end-[1.063rem] after:border-t-[.8rem] after:border-b-[.85rem] after:border-transparent after:border-s-[1.063rem] after:border-solid after:border-s-primary;
  
}
/* Start:: custom-progress-2 */
.progress-item-1,
.progress-item-2,
.progress-item-3 {
  @apply absolute -mt-1 z-[1] h-[0.938rem] w-[0.98rem] rounded-[1.563rem] bg-light;
}
.progress-item-1 {
  @apply start-[25%];
}
.progress-item-2 {
  @apply start-[50%];
}
.progress-item-3 {
  @apply start-[75%];
}
/* End:: custom-progress-2 */
/* Start:: custom-progress-3 */
.custom-progress-3 {
  @apply visible rounded-[0.625rem];
  .progress-bar {
    @apply relative visible m-[0.313rem] rounded-sm
        before:absolute before:w-1 before:h-1 before:start-[0.125rem] before:bg-white before:rounded-md;
    .progress-bar-value {
      @apply border-[0.125rem] border-solid border-primary w-[2.5rem] h-[1.563rem] leading-[1.563rem] rounded-[3.125rem] bg-white dark:bg-bodybg text-[0.625rem] font-semibold
            text-primary text-center absolute -end-[0.625rem];
      &.primary {
        @apply border-[0.125rem] border-solid border-primary text-primary;
      }
      &.secondary {
        @apply border-[0.125rem] border-solid border-secondary text-secondary;
      }
      &.warning {
        @apply border-[0.125rem] border-solid border-warning text-warning;
      }
      &.info {
        @apply border-[0.125rem] border-solid border-info text-info;
      }
      &.success {
        @apply border-[0.125rem] border-solid border-success text-success;
      }
      &.danger {
        @apply border-[0.125rem] border-solid border-danger text-danger;
      }
    }
  }
}
/* End:: custom-progress-3 */

/* Start:: custom-progress-4 */
.custom-progress-4 {
  &.progress {
    @apply bg-primary/10 rounded-sm;
    &.secondary {
      @apply bg-secondary/10;
      .progress-bar-label {
        @apply text-secondary;
      }
    }
    &.warning {
      @apply bg-warning/10;
      .progress-bar-label {
        @apply text-warning;
      }
    }
    &.info {
      @apply bg-info/10;
      .progress-bar-label {
        @apply text-info;
      }
    }
    &.success {
      @apply bg-success/10;
      .progress-bar-label {
        @apply text-success;
      }
    }
    &.danger {
      @apply bg-danger/10;
      .progress-bar-label {
        @apply text-danger;
      }
    }
    .progress-bar {
      @apply rounded-sm;
    }
    .progress-bar-label {
      @apply absolute end-[0.313rem] font-semibold text-[0.813rem] text-primary;
    }
  }
}
/* End:: custom-progress-4 */
