.choices__inner {
  @apply py-2 px-[0.85rem] bg-white dark:bg-bodybg rounded-md text-defaultsize border border-solid border-inputborder dark:border-defaultborder/10 min-w-[13px] leading-[1.65] focus:dark:border-defaultborder/10 #{!important};
}

.input-group .choices__inner {
  @apply rounded-s-none #{!important};
}

.choices__input {
  @apply p-0 #{!important};
}

.choices__list--single {
  @apply pb-0 ps-0 pe-4 #{!important};
}

.choices[data-type*="select-one"] .choices__input {
  @apply p-[0.625rem] #{!important};
}

.choices[data-type*="select-one"]::after {
  @apply end-[0.7188rem] border-t-textmuted border-b-transparent border-x-transparent dark:border-b-transparent dark:border-x-transparent dark:border-t-textmuted #{!important};
}

.choices[data-type*="select-one"]::after {
  @apply text-textmuted #{!important};
}

.choices[data-type*="select-one"] .choices__input {
  @apply p-[0.625rem] #{!important};
}

.choices__list--dropdown,
.choices__list[aria-expanded] {
  @apply bg-white dark:bg-bodybg border border-solid border-defaultborder dark:border-defaultborder/10 z-[1] #{!important};
}

.choices[data-type*="select-one"] .choices__input {
  @apply border-b border-solid border-defaultborder bg-white dark:bg-bodybg dark:border-defaultborder/10 text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

.choices__list--dropdown .choices__item--selectable.is-highlighted,
.choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
  @apply bg-primary text-white dark:bg-primary dark:text-white #{!important};
}

.choices__list--dropdown .choices__list {
  @apply max-h-[250px] #{!important};
}

.choices[data-type*="select-one"]::after {
  @apply border-textmuted;
}

.choices[data-type*="select-one"]:after {
  @apply h-0 w-0 border-solid border-[#333] absolute end-[11.5px] top-[50%] -mt-[2.5px] pointer-events-none;
}

.choices__input {
  @apply bg-transparent text-black dark:text-white #{!important};
}

.is-focused .choices__inner,
.is-open .choices__inner {
  @apply border-defaultborder dark:border-defaultborder/10;
}

.choices[data-type*="select-multiple"] .choices__button,
.choices[data-type*="text"] .choices__button {
  @apply border-s border-solid border-defaultborder dark:border-defaultborder/10 #{!important};
}

.choices__list--multiple .choices__item {
  @apply bg-primary border border-solid border-primary mb-1 #{!important};
}

.choices[data-type*="select-multiple"] .choices__button,
.choices[data-type*="text"] .choices__button {
  @apply border-s border-white dark:border-defaultborder/10 #{!important};
}

.choices__list--multiple .choices__item {
  @apply mb-1 #{!important};
}

.choices__list--single .choices__item {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

.choices__input {
  @apply mb-0 #{!important};
}

.choices__list--multiple .choices__item {
  @apply rounded-sm py-[0.025rem] px-[0.625rem] #{!important};
}

.form-control-select-sm .choices__inner {
  @apply p-[0.275rem] #{!important};
}

.choices[data-type*="select-one"].is-open::after {
  @apply -mt-[0.156rem] #{!important};
}

.choices__heading {
  @apply border-b border-solid border-defaultborder dark:border-defaultborder/10 text-textmuted #{!important};
}

.choices[data-type*="select-one"]::after {
  @apply end-[0.788rem] start-[inherit] #{!important};
}

.choices[data-type*="select-one"] .choices__button {
  @apply end-0 start-[inherit] me-[1.5625rem] ms-[inherit] #{!important};
}

.choices[data-type*="select-multiple"] .choices__button,
.choices[data-type*="text"] .choices__button {
  @apply mt-0 ms-[8px] mb-0 -me-[4px] ps-4 rtl:ps-0 border-x-0 #{!important};
}

select {
  @apply rtl:bg-left;
  @apply rtl:bg-[0.5rem];
}

.ts-wrapper.single .ts-control:after {
  @apply end-[15px] start-auto #{!important};
}

.choices[data-type*="select-one"] .choices__button {
  @apply dark:invert-[1];
}

.ts-control {
  @apply bg-white dark:bg-bodybg dark:border-defaultborder/10 border-defaultborder shadow-none px-3 #{!important};

  input {
    @apply dark:text-white #{!important};
  }
}

.ts-dropdown .active.create {
  @apply dark:text-white #{!important};
}

.multi {
  .ts-control {
    @apply px-3 #{!important};
  }
}

.ts-dropdown .active {
  @apply dark:bg-light dark:text-defaulttextcolor/70 text-defaulttextcolor;
}

.ts-dropdown.single {
  @apply dark:border-defaultborder/10 border-defaultborder #{!important};
}

.ts-dropdown {
  @apply bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 dark:text-defaulttextcolor/70 text-defaulttextcolor;
}

.ts-dropdown [data-selectable].option {
  @apply dark:hover:bg-light hover:bg-light;
}

.ts-wrapper.single .ts-control {
  @apply bg-none shadow-none dark:text-defaulttextcolor/70 text-defaulttextcolor px-3 #{!important};
}

.ts-wrapper.multi .ts-control [data-value] {
  @apply bg-none border-primary bg-primary #{!important};
}

[class="dark"] {
  .choices[data-type*="select-one"] .choices__button {
    filter: invert(1);
  }
}

/* Start:Apex Charts */
.apexcharts-canvas .apexcharts-element-hidden,
.apexcharts-datalabel.apexcharts-element-hidden,
.apexcharts-hide .apexcharts-series-points {
  @apply hidden;
}

#marketCap {
  .apexcharts-canvas line {
    @apply stroke-defaultborder dark:stroke-defaultborder/10;
  }
}

.apexcharts-tooltip {
  @apply shadow-none #{!important};
}

.apexcharts-tooltip-marker {
  @apply me-[0.625rem];
}

.apexcharts-tooltip.apexcharts-theme-light {
  @apply border border-defaultborder border-solid dark:border-defaultborder/10 bg-white dark:bg-bodybg #{!important};
}

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  @apply bg-white dark:bg-bodybg border border-solid border-defaultborder dark:border-defaultborder/10 #{!important};
}

.apexcharts-grid,
.apexcharts-xaxis,
.apexcharts-grid-borders {
  line {
    @apply stroke-defaultborder dark:stroke-defaultborder/10;
  }
}

button.gridjs-sort {
  @apply relative end-[2rem] ltr:float-right rtl:float-left h-[1.25rem] w-[0.625rem] #{!important};
}

.tabulator-col-title {
  @apply dark:text-defaulttextcolor/70 #{!important};
}

.apexcharts-radialbar-track.apexcharts-track {
  path {
    @apply stroke-light;
  }
}

.apexcharts-menu {
  @apply bg-white dark:bg-bodybg border border-solid border-defaultborder dark:border-defaultborder/10 rounded-md #{!important};
}

.apexcharts-theme-light .apexcharts-menu-item:hover {
  @apply bg-light #{!important};
}

.apexcharts-inner.apexcharts-graphical {
  line.apexcharts-xaxis-tick {
    @apply stroke-transparent #{!important};
  }
}

#column-rotated-labels {
  .apexcharts-xaxis-texts-g {
    @apply translate-y-[2.5rem];
  }
}

#chart-year,
#chart-quarter {
  @apply bg-white dark:bg-bodybg border border-defaultborder border-solid dark:border-defaultborder/10 #{!important};
}

.apexcharts-bar-series.apexcharts-plot-series {
  .apexcharts-series {
    .apexcharts-bar-area {
      @apply stroke-defaultborder dark:stroke-defaultborder/10 #{!important};
    }
  }
}

.apexcharts-treemap {
  .apexcharts-series.apexcharts-treemap-series {
    rect {
      @apply stroke-white dark:stroke-bodybg #{!important};
    }
  }
}

.apexcharts-series.apexcharts-pie-series {
  .apexcharts-pie-area {
    @apply stroke-white dark:stroke-bodybg #{!important};
  }
}

.apexcharts-datalabels-group {
  .apexcharts-text.apexcharts-datalabel-value {
    @apply fill-defaulttextcolor dark:fill-defaulttextcolor/70;
  }
}

#analytics-followers {
  .apexcharts-datalabels-group {
    .apexcharts-text.apexcharts-datalabel-value {
      @apply fill-[#4b9bfa] dark:fill-[#4b9bfa];
    }
  }
}

#analytics-views {
  .apexcharts-datalabels-group {
    .apexcharts-text.apexcharts-datalabel-value {
      @apply fill-warning dark:fill-warning;
    }
  }
}

.apexcharts-radialbar-hollow {
  @apply fill-white dark:fill-bodybg;
}

#sale-value {
  .apexcharts-radialbar-hollow {
    @apply fill-white dark:fill-bodybg;
  }
}

.apexcharts-radar-series.apexcharts-plot-series {

  polygon,
  line {
    @apply stroke-defaultborder dark:stroke-defaultborder/10 #{!important};
  }
}

.apexcharts-pie {

  line,
  circle {
    @apply stroke-defaultborder dark:stroke-defaultborder/10 #{!important};
  }

  text {
    @apply fill-defaulttextcolor dark:fill-defaulttextcolor/70 #{!important};
  }
}

#crm-total-customers,
#crm-total-revenue,
#crm-conversion-ratio,
#crm-total-deals,
#nft-balance-chart,
#total-invested,
#total-investments,
#portfolio-value,
#returns-rate,
#btcCoin,
#ethCoin,
#dshCoin,
#glmCoin {

  .apexcharts-grid,
  .apexcharts-xaxis,
  .apexcharts-grid-borders {
    line {
      @apply stroke-white dark:stroke-bodybg #{!important};
    }
  }
}

.apexcharts-canvas .apexcharts-toolbar {
  @apply z-[1];
}

.apexcharts-subtitle-text {
  @apply fill-textmuted #{!important};
}

.apexcharts-pie {
  .apexcharts-datalabels rect {
    @apply fill-transparent;
  }

  text {
    @apply fill-white dark:fill-defaulttextcolor/70;
  }
}

[dir="rtl"] {
  .apexcharts-canvas {
    direction: ltr;
  }

  .apexcharts-canvas .apexcharts-yaxis {
    .apexcharts-yaxis-texts-g {
      @apply -translate-x-[1.75] translate-y-0;
    }
  }
}

.apexcharts-boxPlot-area {
  @apply stroke-defaulttextcolor dark:stroke-defaulttextcolor/70 #{!important};
}

/* End:Apex Charts */

/* Start:Full Calendar */
.fc-theme-standard .fc-scrollgrid.fc-scrollgrid-liquid {
  @apply border-0 border-t border-solid border-defaultborder dark:border-defaultborder/10 #{!important};
}

.fc-daygrid-block-event .fc-event-time,
.fc-daygrid-block-event .fc-event-title {
  @apply py-0 px-1 #{!important};
}

.fc .fc-button-primary {
  @apply bg-primary border-primary #{!important};
}

.fc .fc-non-business {
  @apply bg-white dark:bg-bodybg #{!important};
}

.fc .fc-button-primary:focus,
.fc .fc-button-primary:not(:disabled).fc-button-active:focus,
.fc .fc-button-primary:not(:disabled):active:focus {
  @apply shadow-none #{!important};
}

.fc-theme-standard td,
.fc-theme-standard th {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 border-t-0 #{!important};
}

.fc-list-table {

  td,
  th {
    @apply border-l-0 border-r-0 #{!important};
  }
}

.fc .fc-daygrid-day.fc-day-today {
  @apply bg-primary/10 #{!important};
}

.fc-theme-standard .fc-list {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 #{!important};
}

.fc .fc-list-event:hover td {
  @apply bg-light #{!important};
}

.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror,
.fc-timegrid-more-link {
  @apply shadow-none #{!important};
}

.fc-theme-standard .fc-list-day-cushion {
  @apply bg-light #{!important};
}

.fc-theme-standard .fc-scrollgrid {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 #{!important};
}

.fc-theme-bootstrap5 .fc-list,
.fc-theme-bootstrap5 .fc-scrollgrid,
.fc-theme-bootstrap5 td,
.fc-theme-bootstrap5 th {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 #{!important};
}

@media (max-width: 420px) {
  .fc-scroller.fc-scroller-liquid {
    @apply overflow-scroll #{!important};
  }
}

@media (max-width: 380px) {
  .fc .fc-daygrid-day-bottom {
    @apply text-[0.75rem] pt-0 pe-[3px] pb-[3px] ps-0 #{!important};
  }

  .fc .fc-daygrid-more-link {
    @apply z-[99] #{!important};
  }
}

@media (max-width: 767.98px) {
  .fc .fc-toolbar {
    @apply block #{!important};
  }

  .fc-toolbar-chunk {
    @apply mt-2;
  }
}

/* End:Full Calendar */

/* Start:Pickers */
.flatpickr-calendar {
  @apply bg-white dark:bg-bodybg shadow-md border border-solid border-defaultborder dark:border-defaultborder/10 text-defaultsize #{!important};
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply text-black/90 fill-black/90 #{!important};
}

.flatpickr-monthDropdown-months,
.numInput {
  @apply text-black #{!important};
}

.flatpickr-day.today.inRange {
  @apply text-primary #{!important};
}

.dayContainer {
  @apply p-1 #{!important};
}

.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
  @apply bg-white dark:bg-bodybg text-defaultsize #{!important};
}

.flatpickr-months .flatpickr-prev-month svg,
.flatpickr-months .flatpickr-next-month svg {
  @apply w-[1.25rem] h-[1.25rem] p-1 fill-primary #{!important};
}

.flatpickr-day.inRange {
  @apply shadow-none #{!important};
}

.flatpickr-calendar.open {
  @apply z-[106] #{!important};
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
  @apply bg-primary/10 rounded-sm #{!important};
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after,
.flatpickr-calendar.arrowBottom:before {
  @apply border-t-textmuted #{!important};
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  @apply border-b-textmuted #{!important};
}

.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n + 1)) {
  @apply shadow-none #{!important};
}

.flatpickr-day {
  @apply text-defaulttextcolor font-medium #{!important};

  &.nextMonthDay,
  &.prevMonthDay {
    @apply opacity-[0.5] #{!important};
  }
}

.flatpickr-day.today {
  @apply bg-primary border-primary text-white dark:text-defaulttextcolor/70 #{!important};
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  @apply bg-primary border-primary text-white dark:text-defaulttextcolor/70 #{!important};
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  @apply bg-defaultbackground dark:bg-light border-defaultborder dark:border-defaultborder/10 #{!important};
}

.flatpickr-day.today:hover {
  @apply bg-primary border-primary text-white dark:text-defaulttextcolor/70 #{!important};
}

.flatpickr-calendar.hasTime .flatpickr-time {
  @apply border-t border-solid border-t-defaultborder dark:border-t-defaultborder/10 #{!important};
}

.flatpickr-calendar.arrowTop:after,
.flatpickr-calendar.arrowTop:before {
  @apply border-b border-solid border-b-defaultborder dark:border-b-defaultborder/10 #{!important};
}

.flatpickr-time .numInputWrapper span.arrowDown:after {
  @apply border-primary/50 #{!important};
}

.flatpickr-time input:hover,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time .flatpickr-am-pm:focus {
  @apply bg-defaultbackground dark:bg-light #{!important};
}

.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
  @apply text-textmuted #{!important};
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month {
  @apply start-0 #{!important};
}

.flatpickr-weekdays {
  @apply bg-primary/10 #{!important};
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply top-0 pt-[0.313rem] px-[0.313rem] pb-0 text-primary fill-primary #{!important};
}

@media (min-width: 420px) {
  .flatpickr-time .flatpickr-am-pm {
    @apply pt-0 pe-[1.875rem] pb-0 ps-2;
  }
}

.flatpickr-weekdays {
  @apply border-b border-solid border-b-defaultborder dark:border-b-defaultborder/10 #{!important};
}

.numInputWrapper span.arrowUp {
  @apply -top-[0.125rem] #{!important};
}

.flatpickr-current-month .numInputWrapper {
  @apply w-[3.5rem] #{!important};
}

.flatpickr-calendar.hasTime {
  @apply w-auto;
}

.flatpickr-months {
  .numInputWrapper span {
    @apply end-0 #{!important};
  }
}

@media (max-width: 575.98) {
  .flatpickr-calendar {
    @apply w-[250px] #{!important};
  }
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
  @apply -mt-4 mr-[2.25rem] mb-0 ml-0 #{!important};
}

.flatpickr-current-month .flatpickr-monthDropdown-months,
.flatpickr-current-month input.cur-year {
  @apply pb-0 ps-0 pe-2 text-[0.875rem] font-medium #{!important};
}

.flatpickr-months .flatpickr-prev-month:hover,
.flatpickr-months .flatpickr-next-month:hover {
  @apply stroke-primary;
}

.flatpickr-day {
  @apply rounded-md #{!important};
}

.numInputWrapper:hover {
  @apply bg-transparent #{!important};
}

.numInputWrapper span {
  @apply border-0 #{!important};
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  @apply border-primary #{!important};
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  @apply fill-white dark:fill-black #{!important};
}

.numInputWrapper span:hover {
  @apply bg-transparent #{!important};
}

.numInputWrapper span.arrowUp:after {
  @apply border-s-[0.25rem] border-e-[0.25rem] border-b-[0.25rem] border-solid border-b-primary/50 top-[75%] #{!important};
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
  @apply border-t-primary #{!important};
}

.numInputWrapper span.arrowDown:after {
  @apply border-s-[0.25rem] border-e-[0.25rem] border-b-[0.25rem] border-solid border-b-primary/50 top-[15%] #{!important};
}

span.flatpickr-weekday {
  @apply text-primary/80 font-[700] #{!important};
}

.flatpickr-months .flatpickr-month {
  @apply text-primary fill-primary #{!important};
}

.flatpickr-monthDropdown-months,
.numInput {
  @apply text-primary #{!important};
}

.pcr-app {
  @apply bg-white dark:bg-bodybg #{!important};
}

.pcr-app .pcr-interaction .pcr-result {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 bg-defaultbackground dark:bg-bodybg2 #{!important};
}

.theme-container,
.theme-container1,
.theme-container2 {
  button {
    @apply hidden;
  }
}

.pcr-app[data-theme="classic"] .pcr-selection .pcr-color-preview {
  @apply me-3 #{!important};
}

.pcr-app[data-theme="classic"] .pcr-selection .pcr-color-chooser,
.pcr-app[data-theme="classic"] .pcr-selection .pcr-color-opacity {
  @apply ms-3 #{!important};
}

.flatpickr-weekwrapper .flatpickr-weeks {
  @apply shadow-sm #{!important};
}

/* End:Pickers */

/* Start::Gallery */
.glightbox {
  @apply overflow-hidden;
}

@media (min-width: 769px) {
  .gslide-image img {
    @apply rounded-md;
  }

  .glightbox-clean .gclose,
  .glightbox-clean .gnext,
  .glightbox-clean .gprev {
    @apply bg-[#ffffff0d] w-[2.5rem] h-[2.5rem] p-[0.875rem] #{!important};
  }
}

/* End::Gallery */

/* Start::Calendar */
#external-events .fc-event {
  @apply cursor-move pb-[0.4rem] py-[0.375rem] px-3 text-[0.75rem] rounded-[0.35rem];
}

#calendar-container {
  @apply relative z-[1];
}

#calendar {
  @apply max-w-[68.75rem] my-[1.25rem] mx-auto;
}

/* End::Calendar */

/* Start::Leaflet Maps */
#map,
#map1,
#map-popup,
#map-custom-icon,
#interactive-map {
  @apply h-[18.75rem] z-[10] relative;
}

/* End::Leaflet Maps */

/* Start::Vector Maps */
#vector-map,
#marker-map,
#marker-image-map,
#lines-map,
#us-map,
#canada-map,
#spain-map,
#russia-map {
  @apply h-[21.875rem];
}

.jvm-tooltip {
  @apply bg-[#1a1c1e] #{!important};
}

#vector-map,
#marker-map,
#marker-image-map,
#lines-map,
#visitors-countries,
#users-map {
  #jvm-regions-group path {
    @apply fill-light #{!important};
  }
}

.jvm-zoom-btn {
  @apply flex items-center justify-center;
}

#jvm-markers-labels-group text {
  @apply fill-textmuted;
}

/* End::Vector Maps */

/* Start::Google Maps */
#google-map,
#google-map-overlay,
#map-layers,
#map-markers,
#streetview-map,
#map-geofencing {
  @apply h-[18.75rem];
}

.google-map-overlay {
  @apply block text-center text-white dark:text-defaulttextcolor/70 text-[1.25rem] opacity-[0.8] leading-[0.875rem] bg-primary border-solid border-[0.188rem] border-primary rounded-sm shadow-sm py-0 px-1;
}

.google-overlay_arrow {
  @apply start-[50%] ms-[-1rem] w-0 h-0 absolute;
}

.google-overlay_arrow.above {
  @apply -bottom-[0.938rem] border-s-[0.938rem] border-transparent border-e-[1rem] border-solid border-e-transparent border-t-[1rem] border-t-[#336699];
}

.google-overlay_arrow.below {
  @apply -bottom-[0.938rem] border-s-[1rem] border-transparent border-e-[1rem] border-solid border-e-transparent border-t-[1rem] border-t-[#336699];
}

/* End::Google Maps */

/* Start::Apex Charts */
#pie-basic,
#donut-update,
#pie-monochrome,
#donut-gradient,
#donut-pattern,
#pie-image,
#polararea-basic,
#polararea-monochrome {
  .apexcharts-canvas {
    @apply my-0 mx-auto;
  }
}

.apexcharts-legend-text {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 ps-[0.9375rem] -ms-[0.625rem] #{!important};
}

.apexcharts-text {

  &.apexcharts-yaxis-label,
  &.apexcharts-xaxis-label {
    tspan {
      @apply fill-textmuted;
    }
  }
}

.apexcharts-canvas .apexcharts-series.apexcharts-heatmap-series rect {
  @apply stroke-white dark:stroke-bodybg;
}

.apexcharts-canvas .apexcharts-series-markers.apexcharts-series-bubble circle {
  @apply stroke-white dark:stroke-bodybg;
}

.apexcharts-yaxis .apexcharts-text {
  @apply fill-textmuted;
}

/* End::Apex Charts */

/* Start::Chartjs Charts */
.chartjs-chart {
  @apply max-h-[18.75rem];
}

/* Start::Chartjs Charts */

/* Start::Apex Column Charts */
#chart-year,
#chart-quarter {
  @apply w-[96%] max-w-[48%] shadow-none ps-0 pt-[1.25rem] bg-white dark:bg-bodybg border border-solid border-[#ddd];
}

#chart-year {
  @apply float-left relative transition-all ease-linear duration-[1s] z-[3];
}

#chart-year.chart-quarter-activated {
  @apply translate-x-0 transition-all ease-linear duration-[1s];
}

#chart-quarter {
  @apply float-left relative -z-[2] transition-all ease-linear duration-[1s];
}

#chart-quarter.active {
  @apply transition-all ease-linear duration-[1.1s] translate-x-0 z-[1];
}

@media screen and (min-width: 480px) {
  #chart-year {
    @apply translate-x-[50%];
  }

  #chart-quarter {
    @apply -translate-x-[50%];
  }
}

/* End::Apex Column Charts */

/* Start::ECharts */
.echart-charts {
  @apply h-[20rem];
}

#echart-funnel {
  div:nth-child(3) {
    @apply bg-light #{!important};

    textarea {
      @apply bg-white dark:bg-bodybg2 border-defaultborder text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
    }

    h4 {
      @apply text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
    }
  }
}

#echart-stacked-line div:nth-child(2)>div>div>div>div>div>span:nth-child(1) {
  @apply me-1 ms-0 #{!important};
}

/* End::ECharts */

/* Start::dropzone */
.dropzone {
  @apply border-[0.125rem] border-dashed border-defaultborder dark:border-defaultborder/10 bg-transparent #{!important};

  .dz-message .dz-button {
    @apply text-[1.25rem] text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
  }
}

.dropzone .dz-preview {
  @apply rounded-[1.25rem];
}

/* End::dropzone */
/* Start Filepond Styles */
.filepond--credits {
  @apply hidden;
}

.filepond--drop-label.filepond--drop-label label {
  @apply dark:text-defaulttextcolor/70;
}

.filepond--panel-root {
  @apply bg-transparent border-dashed border-gray-200 dark:border-defaulttextcolor/10 rounded-sm #{!important};
}

.filepond.circular-filepond {
  .filepond--panel-root {
    @apply rounded-full #{!important};
  }
}

.circular-filepond.filepond--root[data-style-panel-layout~="circle"] .filepond--file [data-align*="left"] {
  @apply start-[40%];
}

.circular-filepond.filepond--root[data-style-panel-layout~="circle"] {
  @apply w-32 h-32 my-0 mx-auto #{!important};
}

.filepond--root {
  @apply mb-0 #{!important};
}

/* End Filepond Styles */
/* Start::filepond */
.filepond--drop-label {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 rounded-[0.3rem] #{!important};
}

.filepond--credits {
  @apply hidden;
}

.filepond--panel-root {
  @apply bg-white dark:bg-bodybg border-[0.125rem] border-dashed border-defaultborder dark:border-defaultborder/10 #{!important};
}

.filepond--drop-label.filepond--drop-label label {
  @apply p-[1.5rem] text-defaultsize #{!important};
}

.filepond--root {
  @apply mb-0 #{!important};
}

.filepond--file {
  @apply bg-primary #{!important};
}

.single-fileupload {
  @apply w-[8rem] h-[8rem] my-0 mx-auto #{!important};
}

/* End::filepond */

/* Start:: quill editor */
.ql-toolbar.ql-snow,
.ql-container.ql-snow {
  @apply border border-solid border-defaultborder dark:border-defaulttextcolor/10 #{!important};
}

.ql-snow .ql-picker {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

.ql-snow .ql-stroke,
.ql-snow .ql-stroke.ql-fill {
  @apply stroke-defaulttextcolor dark:stroke-defaulttextcolor/70 #{!important};
}

.ql-snow .ql-fill {
  @apply fill-defaulttextcolor dark:fill-defaulttextcolor/70 #{!important};
}

.ql-toolbar.ql-snow+.ql-container.ql-snow {
  @apply border-t-0 #{!important};
}

.ql-snow .ql-picker-options .ql-picker-item {
  @apply pt-0 pb-0 #{!important};
}

#product-features {
  @apply max-h-[12.5rem] overflow-y-scroll border-b border-defaultborder dark:border-defaultborder/10;
  .ql-container {
    @apply border-b-0 #{!important};
  }
}

.ql-editor {
  @apply min-h-[15.62rem] overflow-visible #{!important};
}

.ql-snow .ql-formats {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 rounded-md;
}

.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  @apply border-defaultborder dark:border-defaultborder/10 rounded-md #{!important};
}

.ql-snow .ql-picker-options {
  @apply bg-white dark:bg-bodybg #{!important};
}

.ql-snow .ql-tooltip {
  @apply bg-white dark:bg-bodybg border-defaultborder dark:border-defaulttextcolor/10 border border-solid shadow-md text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

.ql-snow .ql-tooltip input[type="text"] {
  @apply border-defaultborder dark:border-defaulttextcolor/10 border border-solid shadow-md text-defaulttextcolor dark:text-defaulttextcolor/70 outline-0 #{!important};
}

.ql-snow .ql-tooltip {
  @apply translate-x-[12.5rem] z-[100] #{!important};
}

.ql-snow .ql-picker-label {
  @apply ps-2 pe-[0.125rem] #{!important};
}

.ql-snow .ql-formats .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  @apply end-0 start-auto #{!important};
}

.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

.ql-editor {
  @apply text-start #{!important};
}

.ql-snow .ql-editor {
  @apply p-[1.25rem];
}

.ql-bubble {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 rounded-md #{!important};
}

.ql-editor li:not(.ql-direction-rtl)::before {
  @apply -ms-[1.5em] me-[0.3em] text-start #{!important};
}

.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
  @apply ps-[1.5rem] #{!important};
}

.ql-toolbar.ql-snow .ql-formats {
  @apply m-1;
}

.ql-tooltip.ql-editing {
  @apply  top-0 #{!important};
}

.ql-bubble .ql-toolbar .ql-formats:first-child {
  @apply ms-[12px] me-0;
}

.ql-bubble .ql-toolbar .ql-formats {
  @apply pt-[8px] ms-0 pb-[8px] me-[12px];
}

/* end:: quill editor */

/* Start:: select2 */
.select2.select2-container {
  @apply w-full #{!important};
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 leading-[2.25] border-defaultborder dark:border-defaultborder/10 rounded-md #{!important};
}

.select2-container--default .select2-selection--single {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaulttextcolor/10 rounded-md #{!important};
}

.select2-container .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__arrow {
  @apply h-[2.25rem] end-3 start-auto #{!important};
}

.select2-dropdown {
  @apply bg-white dark:bg-bodybg border border-defaultborder dark:border-defaulttextcolor/10 rounded-md #{!important};
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  @apply border border-defaultborder dark:border-defaulttextcolor/10 rounded-md #{!important};
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

.select2-results__option--selectable {
  @apply text-defaultsize;
}

.select2-container--default .select2-results__option--selected {
  @apply bg-primary text-white #{!important};

  &.select2-results__option--highlighted {
    @apply bg-primary text-white #{!important};
  }
}

.select2-search__field {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};

  &:focus-visible {
    @apply outline-none #{!important};
  }
}

.select2-container--default .select2-selection--multiple {
  @apply border dark:bg-bodybg border-defaultborder dark:border-defaulttextcolor/10 rounded-md #{!important};
}

.select2-container .select2-selection--multiple {
  @apply min-h-[2.25rem] #{!important};
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  @apply bg-primary text-white border border-solid border-primary rounded-[0.15rem] mt-[0.375rem] #{!important};
}

.select2-selection--multiple .select2-search__field {
  @apply bg-transparent #{!important};
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  @apply border-e border-white/10 text-white -top-[0.3rem] font-medium text-[1.125rem] #{!important};
}

.select2-selection--multiple .select2-selection__choice__display {
  @apply text-[0.75rem] px-3 #{!important};
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:focus {
  @apply bg-primary #{!important};
}

.select2-results__option span img,
.select2-selection__rendered span img {
  @apply w-[1.45rem] h-[1.45rem] rounded-[1.25rem] me-1;
}

.select2-container .select2-search--inline .select2-search__field {
  @apply mt-2 #{!important};
}

.select2-container--disabled {

  &.select2-container--default .select2-selection--single .select2-selection__rendered,
  .select2-selection.select2-selection--multiple {
    @apply bg-white dark:bg-bodybg #{!important};
  }
}

.select2-container--default .select2-selection--single .select2-selection__clear {
  @apply font-normal h-[1.25rem] text-[1.5625rem] w-[1.25rem] absolute end-[0.625rem] #{!important};
}

.select2-selection__clear {
  @apply text-textmuted;
}

.select2-dropdown {
  @apply z-[10] #{!important};
}

.select2-container--default .select2-results__option--selected.select2-results__option--highlighted {
  @apply bg-primary text-white #{!important};
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  @apply bg-[#f7f8f9] text-defaulttextcolor #{!important};
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  @apply bg-primary text-white border border-primary border-solid rounded-[0.15rem] mt-[0.375rem] #{!important};
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  @apply border border-solid rounded-md #{!important};
}

.select2-selection__choice__remov {
  @apply text-white #{!important};
}

.select2-results__option span img,
.select2-selection__rendered span img {
  @apply w-[1.45rem] h-[1.45rem] rounded-[1.25rem] me-1 inline-flex #{!important};
}

.select2-container--default .select2-results__option--selected {
  @apply bg-primary text-white #{!important};
}

.select2-container--default .select2-selection--multiple {
  @apply border-defaultborder #{!important};
}

.select2-dropdown {
  @apply border border-solid border-defaultborder #{!important};
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  @apply border border-solid border-defaultborder #{!important};
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
  @apply border-e #{!important};
}

.select2-container--default .select2-selection--single .select2-selection__clear {
  @apply me-[20px] ms-0 ps-0 #{!important};
}

/* End:: select2 */

.swiper-button-next,
.swiper-button-prev {
  @apply w-[1.563rem] h-[1.563rem] text-white bg-black/30 rounded-md #{!important};
}

// .swiper-preview-details {
//   .swiper-button-next {
//     @apply bg-black/10 text-defaulttextcolor #{!important};
//   }
//   .swiper-button-prev {
//     @apply bg-black/10 text-defaulttextcolor #{!important};
//   }
// }
// .swiper-button-next,
// .swiper-button-prev {
//   @apply w-[1.563rem] h-[1.563rem] text-white bg-white/20 rounded-md #{!important};
// }

// .swiper-button-next,
// .swiper-button-prev{
//     @apply bg-transparent #{!important};
// }
.swiper-pagination-bullet {
  @apply w-[1.25rem] h-[0.25rem] rounded-md bg-white #{!important};
}

.custom-pagination .swiper-pagination .swiper-pagination-bullet {
  @apply w-[1.25rem] h-[1.25rem] text-black #{!important};
}

.custom-pagination .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  @apply bg-white text-black #{!important};
}

.swiper-pagination-bullet-active {
  @apply bg-white #{!important};
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  @apply fill-success bg-success #{!important};
}

.swiper-pagination {
  @apply text-white;
}

[dir="rtl"] {
  pre[class*="language-"]>code {
    @apply border-l-0 text-right #{!important};
  }
}

/* End:: prism js */

/* Start:: Draggable Cards */
#draggable-left .card,
#draggable-right .card {
  @apply touch-none;
}

/* End:: Draggable Cards */

/* Start:: Full Calendar */

.fullcalendar-events-activity {
  li {
    @apply mb-3 text-[0.8125rem] py-1 pe-4 ps-[2rem] relative before:absolute before:w-3 before:h-3 before:border-[0.125rem] before:border-solid before:border-primary/30 before:rounded-[3.125rem] before:bg-none before:start-1 before:top-[0.563rem] after:absolute after:h-full after:bg-transparent after:border-e-[2px] after:border-dashed after:border-primary/10 after:start-[0.563rem] after:top-[1.25rem];

    &:last-child {
      @apply mb-0 after:border-e-0 after:border-dashed after:border-defaultborder;
    }
  }
}

#full-calendar-activity {
  @apply max-h-[21rem];
}

/* End:: Full Calendar */

/* Start::Icons Page */
.icons-list {
  @apply list-none mt-0 -me-4 ms-0 p-0 flex flex-wrap #{!important};

  .icons-list-item {
    @apply text-center h-12 w-12 flex items-center justify-center border border-solid border-defaultborder dark:border-defaultborder/10 m-1 rounded-sm #{!important};

    i {
      @apply text-[1.05rem] text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
    }
  }
}

.fe {
  @apply text-inherit;
}

/* End::Icons Page */

/* Start::Scrollspy */
.scrollspy-example {
  @apply h-[12.5rem] mt-2 overflow-auto;
}

.scrollspy-example-2 {
  @apply h-[21.875rem] overflow-auto;
}

.scrollspy-example-3 {
  @apply h-[13.75rem] overflow-auto;
}

.simple-list-example-scrollspy .active {
  @apply bg-primary text-white #{!important};
}

.scrollspy-example-4 {
  @apply h-[12.5rem] mt-2 overflow-auto;
}

#navbar-example3 {
  .nav-pills .nav-link {
    @apply py-2 px-4;
  }

  .active,
  .nav-pills .show>.nav-link {
    @apply text-white bg-primary #{!important};
  }
}

/* End::Scrollspy */

//g-maps//
#google-map,
#google-map-overlay,
#map-geofencing,
#map-layers,
#map-markers,
#streetview-map {
  @apply h-[18.75rem];
}

#interactive-map,
#map,
#map-custom-icon,
#map-popup,
#map1 {
  @apply h-[18.75rem] z-[10];
}

//g-maps//
#project-descriptioin-editor {
  @apply h-[200px] overflow-auto #{!important};
}

//start-chats//
.chartjs-chart {
  @apply max-h-[18.75rem] #{!important};
}

.echart-charts {
  @apply h-[20rem] #{!important};
}

//end-charts//

//start-prism//
pre[class*="language-"]>code {
  @apply shadow-none bg-light border border-solid border-defaultborder dark:border-defaultborder/10 rounded-md bg-none #{!important};
}

pre[class*="language-"] {
  @apply bg-none bg-transparent border-0 #{!important};
}

pre[class*="language-"]:before {
  @apply content-none #{!important};
}

pre[class*="language-"]:after {
  @apply content-none #{!important};
}

pre[class*="language-"]>code {
  @apply max-h-[300px];
}

//end-prism//

//start-range-slider//
.noUi-handle {
  @apply border border-solid border-white rounded-[3px] bg-white dark:bg-bodybg dark:border-defaultborder/10 cursor-default shadow-none #{!important};
}

.noUi-target {
  @apply bg-defaultbackground border border-solid border-defaultborder shadow-sm #{!important};
}

.noUi-horizontal {
  @apply h-[0.35rem] dark:bg-bodybg dark:border-defaultborder/10 #{!important};
}

.noUi-vertical {
  @apply w-[0.35rem] dark:bg-bodybg dark:border-defaultborder/10 #{!important};
}

.noUi-connect {
  @apply bg-primary #{!important};
}

.noUi-horizontal .noUi-handle {
  @apply w-[1rem] h-[1rem] -end-[-0.063rem] #{!important};
}

.noUi-handle:after,
.noUi-handle:before {
  @apply h-[0.375rem] w-[1px] start-[0.3rem] top-1 #{!important};
}

.noUi-handle:after {
  @apply start-[0.45rem] #{!important};
}

#slider-round {
  @apply h-[0.625rem] #{!important};

  .noUi-handle {
    @apply h-[1.125rem] w-[1.125rem] -top-[0.313rem] -end-[0.563rem] rounded-full bg-primary #{!important};

    &:before,
    &:after {
      @apply hidden #{!important};
    }
  }
}

#slider-square {
  @apply h-[0.625rem] #{!important};

  .noUi-handle {
    @apply h-[1.125rem] w-[1.125rem] -top-[0.45rem] -end-[0.563rem] rounded-none bg-primary #{!important};

    &:before,
    &:after {
      @apply hidden #{!important};
    }
  }
}

#color1,
#color2,
#color3 {
  @apply m-[0.625rem] inline-block h-[12.5rem] #{!important};
}

#colorpicker {
  @apply h-[15rem] w-[19.375rem] p-[0.625rem] border border-solid border-defaultborder #{!important};
}

#result {
  @apply my-[4.25rem] me-0 ms-[4rem] h-[6.25rem] w-[6.25rem] inline-block align-top text-gray-50 bg-gray-50 border border-solid border-white shadow-sm;
}

#color1 .noUi-connect {
  @apply bg-danger #{!important};
}

#color2 .noUi-connect {
  @apply bg-secondary #{!important};
}

#color3 .noUi-connect {
  @apply bg-primary #{!important};
}

#slider-hide .noUi-tooltip {
  @apply hidden;
}

#slider-hide .noUi-active .noUi-tooltip {
  @apply block;
}

#slider-toggle {
  @apply h-[3.125rem];
}

.noUi-vertical .noUi-handle {
  @apply w-4 h-4 #{!important};
}

.c-1-color {
  @apply bg-secondary #{!important};
}

.c-2-color {
  @apply bg-warning #{!important};
}

.c-3-color {
  @apply bg-info #{!important};
}

.c-4-color {
  @apply bg-danger #{!important};
}

.c-5-color {
  @apply bg-indigomain #{!important};
}

#secondary-colored-slider {
  .noUi-connect {
    @apply bg-secondary #{!important};
  }
}

#warning-colored-slider {
  .noUi-connect {
    @apply bg-warning #{!important};
  }
}

#info-colored-slider {
  .noUi-connect {
    @apply bg-info #{!important};
  }
}

#success-colored-slider {
  .noUi-connect {
    @apply bg-success #{!important};
  }
}

#danger-colored-slider {
  .noUi-connect {
    @apply bg-danger #{!important};
  }
}

.noUi-marker {
  @apply bg-defaultborder dark:bg-white/10 #{!important};
}

.rangeslider__handle {
  @apply rtl:dir-rtl rtl:left-0 rtl:text-right #{!important}
}

.rangeslider__fill {
  @apply rtl:w-full #{!important};
}

//end-range-slider//

/*Color Picker*/
.pickr .pcr-button {
  @apply overflow-hidden rounded-sm #{!important};
}

.pcr-app {
  @apply rounded-sm z-[9999999999] #{!important};
}

/*Color Picker*/

//callender//

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply fill-primary #{!important};
}

.flatpickr-day.today {
  @apply border-primary bg-primary text-warning #{!important};
}

.flatpickr-calendar {
  @apply border-s border-solid border-defaultborder dark:border-defaultborder/10 dark:bg-bodybg #{!important};
}

.flatpickr-day.selected {
  @apply bg-primary text-white border-primary hover:bg-primary hover:text-white hover:border-primary #{!important};
}

span.flatpickr-weekday {
  @apply text-primary #{!important};
}

.flatpickr-day.today {
  @apply text-white #{!important};
}

.flatpickr-day {
  @apply rounded-md #{!important};
}

.flatpickr-day.today:hover {
  @apply bg-primary border-primary text-white #{!important};

  &.active {
    @apply bg-primary border-primary text-white #{!important};
  }
}

.flatpickr-weekdays {
  @apply bg-primary/10 text-primary #{!important};
}

.flatpickr-month {
  @apply bg-primary/10 text-primary text-[0.813rem] #{!important};
}

//callender//

//editor//
.ql-toolbar.ql-snow {
  @apply rounded-t-[0.3rem] #{!important};
}

.ql-container.ql-snow,
.ql-toolbar.ql-snow {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 #{!important};
}

.ql-bubble {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 rounded-md #{!important};
}

//editor//

/* Start:: grid js tables */
.gridjs-table {
  @apply w-full;
}

table.gridjs-table {
  @apply text-start text-defaultsize font-medium #{!important};
}

.gridjs-wrapper {
  @apply shadow-none border-b-0 border-t-0 rounded-none #{!important};

  &:nth-last-of-type(2) {
    @apply rounded-none #{!important};
  }
}

.gridjs-container {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

th.gridjs-th {
  @apply bg-white dark:bg-bodybg border border-solid border-defaultborder dark:border-defaultborder/10 text-defaulttextcolor dark:text-defaulttextcolor/70 p-3 text-start #{!important};
}

td.gridjs-td {
  @apply border border-solid border-defaultborder dark:border-defaultborder/10 p-3 #{!important};
}

.gridjs-tbody,
td.gridjs-td {
  @apply bg-white dark:bg-bodybg #{!important};
}

.gridjs-footer {
  @apply bg-white dark:bg-bodybg border-b-0 border-transparent rounded-none border-t-0 shadow-none pt-3 #{!important};
}

.gridjs-pagination {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

.gridjs-pagination .gridjs-pages button:first-child {
  @apply rounded-ss-md rounded-es-md rounded-ee-none rounded-se-none #{!important};
}

.gridjs-pagination .gridjs-pages button:last-child {
  @apply rounded-es-none rounded-ss-none rounded-ee-md rounded-se-md #{!important};
}

@media (max-width: 575.98px) {
  .gridjs-search-input {
    @apply w-[12.5rem] #{!important};
  }
}

.gridjs-pagination .gridjs-pages button:disabled,
.gridjs-pagination .gridjs-pages button:hover:disabled,
.gridjs-pagination .gridjs-pages button[disabled] {
  @apply bg-white dark:bg-bodybg text-defaulttextcolor dark:text-defaulttextcolor/70 #{!important};
}

.gridjs-pagination .gridjs-pages button {
  @apply bg-white dark:bg-bodybg border border-solid border-defaultborder dark:border-defaultborder/10 py-[0.375rem] px-3 #{!important};

  &:focus {
    @apply shadow-none me-0 #{!important};
  }
}

.gridjs-pagination .gridjs-pages button.gridjs-currentPage {
  @apply font-semibold bg-primary text-white #{!important};
}

.gridjs-pagination .gridjs-pages {
  @apply float-right #{!important};
}

input.gridjs-input {
  @apply bg-white border border-defaulttextcolor dark:border-defaultborder/10 text-defaultsize py-[0.375rem] px-3;

  &:focus {
    @apply font-semibold border border-solid border-primary #{!important};
  }
}

th.gridjs-th-fixed {
  @apply bg-light #{!important};
}

#grid-header-fixed {
  .gridjs-wrapper {
    @apply border-t border-b border-solid border-defaultborder dark:border-defaultborder/10 #{!important};
  }

  .gridjs-container .gridjs-wrapper .gridjs-thead .gridjs-tr th {
    @apply -top-[0.1px] #{!important};
  }
}

.gridjs-tr {
  @apply border text-start border-defaultborder dark:border-defaultborder/10 #{!important};
}

input.gridjs-input {
  @apply bg-white dark:bg-bodybg;
}

[dir="rtl"] {
  .tabulator-footer-contents {
    @apply dir-ltr #{!important};
  }

  .ql-editor .ql-align-right {
    @apply text-left #{!important};
  }
}

//// styles /////
:is(.dark [type="radio"]):checked {
  @apply bg-primary;
}

@media (max-width: 575.98px) {
  #crm-revenue-analytics .apexcharts-canvas .apexcharts-toolbar {
    inset-block-start: 0.75rem !important;
    inset-inline-end: 0 !important;
  }
}

#product-features {
  @apply rounded-b-sm #{!important};
}

.ti-btn-icon {
  @apply w-[2.313rem] h-[2.313rem] text-[0.95rem];
}

.swal2-title {
  @apply text-defaulttextcolor #{!important};
}

[type="checkbox"]:focus {
  @apply outline-none ring-0 border-defaultborder #{!important};
}

#security,
#buycrypto,
.companies-search-input {

  .choices__list--dropdown .choices__item--selectable::after,
  .choices__list[aria-expanded] .choices__item--selectable::after {
    @apply hidden #{!important};
  }

  .choices__list--dropdown .choices__item--selectable,
  .choices__list[aria-expanded] .choices__item--selectable {
    @apply p-3 #{!important};
  }
}

.newproject {
  .choices__inner {
    @apply w-[120px] #{!important};
  }

  .choices__list--dropdown .choices__item--selectable::after,
  .choices__list[aria-expanded] .choices__item--selectable::after {
    @apply hidden #{!important};
  }

  .choices__list--dropdown .choices__item--selectable,
  .choices__list[aria-expanded] .choices__item--selectable {
    @apply p-3 #{!important};
  }
}

.crypto-buy-sell {
  .choices__inner {
    @apply min-w-[5rem] border-inputborder dark:border-white/10 #{!important};
  }

  .choices__list--dropdown .choices__item--selectable::after,
  .choices__list[aria-expanded] .choices__item--selectable::after {
    @apply hidden #{!important};
  }

  .choices__list--dropdown .choices__item--selectable,
  .choices__list[aria-expanded] .choices__item--selectable {
    @apply pe-0 #{!important};
  }
}

[dir="rtl"] {
  .ts-control {
    input {
      @apply ms-[10px] #{!important};
    }
  }
}