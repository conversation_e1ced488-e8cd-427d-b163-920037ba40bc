.bg-banner-2 {
  @apply relative top-0 bg-[url('../public/assets/images/_generic/forest.png')] bg-cover bg-center bg-no-repeat z-[0] before:absolute before:w-full before:h-full before:bg-primary/80 before:inset-0 before:-z-[1] ;
}
.featured-jobs {
  @apply hidden border border-solid border-defaultborder dark:border-defaultborder/10;
}
.feature-style .feature-style-icon {
  @apply relative w-[4rem] h-[4rem] leading-[4rem] text-[1.25rem] items-center justify-center inline-flex rounded-full font-medium #{!important};
}
.feature-style .feature-style-icon img, .feature-style .feature-style-icon svg {
  @apply w-[2.5rem] h-[2.5rem];
}
.feature-style {
  @apply relative hidden overflow-hidden before:absolute before:w-[4rem] before:h-[4rem] before:-end-[1.5rem] before:-bottom-[1.5rem] before:bg-primary/10 before:rounded-full;
}
.bg-banner {
  @apply bg-primary dark:bg-primary text-white relative overflow-hidden z-[1] before:absolute before:h-full before:w-full before:top-0 before:bg-[url('../public/assets/images/_generic/landscape.png')] before:bg-no-repeat
  before:bg-bottom before:bg-cover before:opacity-[0.1] before:transition-all before:ease-linear before:duration-[0.3sec] before:-z-[1];
}
.featured-card-4 svg {
  @apply w-[1.5rem] h-[1.5rem];
}
// .landing-footer-list li {
//   @apply  py-0 px-[12px];
// }
.landing-body .landing-main-footer .landing-footer-list li:not(:first-child) {
  @apply relative before:absolute before:w-[0.3rem] before:h-[0.3rem] before:border before:border-solid before:border-white before:rounded-full before:bg-transparent 
  before:-start-[0.2rem] before:top-2;
}
.custom-form-group {
  @apply relative flex items-center;
}
.custom-form-group .custom-form-btn {
  @apply absolute end-2 rounded-[0.3rem] flex items-center justify-center bg-white dark:bg-bodybg;
}

.custom-form-group .custom-form-btn .gps-location {
  @apply text-textmuted opacity-[0.6] text-[1.0625rem] leading-[0] me-[10px];
}
.landing-body.jobs-landing .landing-main-footer .landing-footer-list li {
  @apply inline-block px-3 py-0;
}
