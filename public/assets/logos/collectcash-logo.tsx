export const CollectCashLogo = ({
  favicon,
  darkMode,
}: {
  favicon?: boolean;
  darkMode?: boolean;
}) => {
  return favicon ? (
    <svg width="32" height="32" viewBox="0 0 214 214" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="110" cy="110" r="104" fill="#111C9D"/>
<path d="M59.0972 140.414C64.9745 147.704 72.3231 153.469 80.5169 157.422C88.6794 161.35 97.7106 163.466 107.008 163.466C116.305 163.466 125.336 161.358 133.491 157.422C141.685 153.477 149.033 147.704 154.911 140.414L166 149.724C158.73 158.731 149.691 165.846 139.626 170.692C129.523 175.555 118.402 178.174 107 178.174C95.5975 178.174 84.4768 175.563 74.3735 170.692C64.3093 165.846 55.2625 158.731 48 149.724L59.0894 140.414H59.0972Z" fill="white"/>
<path d="M121.16 130.5H104.657C100.356 132.913 95.2503 134.12 89.3487 134.12C80.098 134.12 72.2135 130.816 65.6716 124.214C59.1298 117.613 55.8667 109.639 55.8667 100.293C55.8667 90.9476 59.1376 82.9818 65.6716 76.3727C72.2056 69.7714 80.098 66.4668 89.3487 66.4668C95.2503 66.4668 100.348 67.6735 104.657 70.0869H121.16V130.492V130.5ZM89.3565 83.0213C84.6492 83.0213 80.6445 84.7091 77.3423 88.0925C74.0324 91.476 72.3774 95.5456 72.3774 100.293C72.3774 105.041 74.0324 109.111 77.3423 112.494C80.6523 115.878 84.657 117.566 89.3565 117.566C94.056 117.566 98.0685 115.815 100.894 112.313C103.728 108.811 105.141 104.805 105.141 100.293C105.141 95.7822 103.728 91.7757 100.894 88.2739C98.0607 84.7722 94.2199 83.0213 89.3565 83.0213Z" fill="white"/>
<path d="M151.84 130.974H135.32V70.4004H151.84V130.974Z" fill="white"/>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M135.32 35V52.3067H151.84V35H135.32Z'
        fill='white'
      />
    </svg>
  ) : (
    <svg width="180" height="40" viewBox="0 0 141 71" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clipPath="url(#clip0_3397_28871)">
<path d="M7.90508 57.5528C8.58573 57.5528 9.1332 57.7616 9.5549 58.1754C9.9766 58.5893 10.1875 59.1858 10.1875 59.965V63.2793H9.27006V60.0992C9.27006 59.5362 9.1295 59.1075 8.85206 58.8092C8.57463 58.511 8.19362 58.3618 7.71273 58.3618C7.23184 58.3618 6.83603 58.5147 6.5475 58.8241C6.25897 59.1336 6.1147 59.581 6.1147 60.17V63.2793H5.18622V57.6535H6.1147V58.455C6.29966 58.168 6.5475 57.9443 6.86193 57.7877C7.17635 57.6311 7.52407 57.5528 7.90508 57.5528ZM16.7941 60.2558C16.7941 60.4347 16.783 60.6211 16.7645 60.8187H12.3034C12.3366 61.3742 12.5253 61.8067 12.8693 62.1161C13.2133 62.4293 13.6276 62.5822 14.1159 62.5822C14.5154 62.5822 14.8521 62.489 15.1184 62.2988C15.3884 62.1124 15.5734 61.8589 15.6844 61.5457H16.6831C16.5352 62.0863 16.2355 62.5262 15.7879 62.8655C15.3403 63.2048 14.7818 63.3726 14.1159 63.3726C13.587 63.3726 13.1135 63.2533 12.6955 63.0146C12.2775 62.776 11.9519 62.433 11.7115 61.9931C11.4748 61.5532 11.3564 61.0387 11.3564 60.4571C11.3564 59.8755 11.4711 59.3647 11.7041 58.9285C11.9334 58.4923 12.259 58.1531 12.677 57.9182C13.095 57.6833 13.5759 57.564 14.1196 57.564C14.6634 57.564 15.1184 57.6796 15.5253 57.9145C15.9322 58.1456 16.2466 58.4662 16.4686 58.8726C16.6905 59.279 16.7978 59.7413 16.7978 60.252L16.7941 60.2558ZM15.836 60.0582C15.836 59.704 15.7584 59.3946 15.603 59.141C15.4476 58.8838 15.2331 58.6899 14.9667 58.5557C14.6967 58.4215 14.4008 58.3544 14.0752 58.3544C13.6055 58.3544 13.2096 58.5035 12.8804 58.8055C12.5512 59.1075 12.3625 59.525 12.3145 60.0582H15.8397H15.836ZM20.3194 57.564C20.8003 57.564 21.2257 57.6684 21.5882 57.8809C21.9507 58.0934 22.2207 58.3581 22.3983 58.6825V57.6572H23.3342V63.4061C23.3342 63.9206 23.2269 64.3754 23.0087 64.7781C22.7904 65.177 22.4797 65.4902 22.0765 65.7176C21.6733 65.945 21.2035 66.0569 20.6671 66.0569C19.9347 66.0569 19.3243 65.8816 18.8323 65.5349C18.344 65.1845 18.0555 64.711 17.9667 64.107H18.8841C18.9877 64.45 19.1948 64.7259 19.5167 64.9347C19.8348 65.1434 20.2195 65.2478 20.6671 65.2478C21.1776 65.2478 21.5919 65.0875 21.9137 64.7669C22.2355 64.4463 22.3983 63.9952 22.3983 63.4136V62.2317C22.2133 62.5598 21.9433 62.8357 21.5845 63.0519C21.2257 63.2719 20.804 63.38 20.3231 63.38C19.8422 63.38 19.3761 63.257 18.9729 63.0109C18.5697 62.7649 18.2516 62.4181 18.0222 61.9745C17.7929 61.5308 17.6745 61.0238 17.6745 60.4571C17.6745 59.8904 17.7892 59.3759 18.0222 58.9434C18.2516 58.5072 18.5697 58.1717 18.9729 57.9331C19.3761 57.6945 19.8274 57.5752 20.3231 57.5752L20.3194 57.564ZM22.3946 60.4608C22.3946 60.0358 22.3095 59.6667 22.1394 59.3535C21.9692 59.0404 21.7399 58.798 21.4513 58.6303C21.1628 58.4625 20.8447 58.3805 20.5006 58.3805C20.1566 58.3805 19.8385 58.4625 19.5537 58.6265C19.2688 58.7906 19.0395 59.0292 18.873 59.3461C18.7029 59.6593 18.6178 60.0321 18.6178 60.4534C18.6178 60.8747 18.7029 61.2587 18.873 61.5756C19.0432 61.8925 19.2688 62.1385 19.5537 62.3063C19.8385 62.4741 20.1529 62.5561 20.5006 62.5561C20.8484 62.5561 21.1628 62.4741 21.4513 62.3063C21.7399 62.1385 21.9692 61.8962 22.1394 61.5756C22.3095 61.2587 22.3946 60.8858 22.3946 60.4608ZM27.3366 63.3726C26.8151 63.3726 26.3416 63.2533 25.9162 63.0146C25.4908 62.776 25.1579 62.433 24.9174 61.9931C24.677 61.5532 24.5549 61.0387 24.5549 60.4571C24.5549 59.8755 24.6807 59.3759 24.9248 58.9323C25.1727 58.4923 25.5093 58.1531 25.9384 57.9145C26.3675 57.6796 26.8447 57.5603 27.3736 57.5603C27.9026 57.5603 28.3835 57.6796 28.8089 57.9145C29.238 58.1493 29.5746 58.4886 29.8225 58.9248C30.0703 59.361 30.1924 59.8718 30.1924 60.4534C30.1924 61.035 30.0666 61.5457 29.8114 61.9894C29.5561 62.4293 29.2121 62.7723 28.7793 63.0109C28.3465 63.2495 27.8619 63.3688 27.3329 63.3688L27.3366 63.3726ZM27.3366 62.5523C27.6696 62.5523 27.9803 62.4741 28.2725 62.3175C28.5648 62.1609 28.8015 61.9223 28.9791 61.6091C29.1603 61.2959 29.2491 60.9119 29.2491 60.4608C29.2491 60.0097 29.1603 59.6257 28.9828 59.3125C28.8052 58.9994 28.5759 58.7645 28.291 58.6079C28.0062 58.455 27.6955 58.3768 27.3625 58.3768C27.0296 58.3768 26.7115 58.455 26.4304 58.6079C26.1492 58.7608 25.9236 58.9956 25.7534 59.3125C25.5833 59.6257 25.4982 60.0097 25.4982 60.4608C25.4982 60.9119 25.5833 61.3071 25.746 61.6203C25.9125 61.9335 26.1344 62.1683 26.4119 62.3249C26.6893 62.4778 26.9963 62.5561 27.3292 62.5561L27.3366 62.5523ZM31.0728 60.4608C31.0728 59.8792 31.1874 59.3722 31.4205 58.936C31.6498 58.4998 31.9717 58.1642 32.3823 57.9256C32.7929 57.687 33.2627 57.5677 33.7916 57.5677C34.476 57.5677 35.042 57.7355 35.4858 58.071C35.9297 58.4066 36.2257 58.8726 36.3662 59.4654H35.3675C35.2713 59.1224 35.0863 58.854 34.8126 58.6526C34.5389 58.455 34.1948 58.3544 33.7879 58.3544C33.259 58.3544 32.8299 58.5371 32.5043 58.9024C32.1788 59.2678 32.0161 59.786 32.0161 60.4571C32.0161 61.1282 32.1788 61.6576 32.5043 62.0267C32.8299 62.3958 33.259 62.5822 33.7879 62.5822C34.1948 62.5822 34.5352 62.4852 34.8052 62.2951C35.0752 62.105 35.2639 61.8291 35.3638 61.4749H36.3625C36.2146 62.049 35.9149 62.5113 35.4674 62.8543C35.0198 63.2011 34.4575 63.3726 33.7879 63.3726C33.259 63.3726 32.7892 63.2533 32.3786 63.0146C31.968 62.776 31.6461 62.4368 31.4168 61.9968C31.1875 61.5606 31.0691 61.0462 31.0691 60.4571L31.0728 60.4608ZM38.0789 56.7438C37.9014 56.7438 37.7534 56.6804 37.6313 56.5574C37.5093 56.4344 37.4464 56.2852 37.4464 56.1063C37.4464 55.9273 37.5093 55.7782 37.6313 55.6552C37.7534 55.5321 37.9014 55.4688 38.0789 55.4688C38.2565 55.4688 38.3934 55.5284 38.5117 55.6552C38.6301 55.7782 38.6893 55.9273 38.6893 56.1063C38.6893 56.2852 38.6301 56.4344 38.5117 56.5574C38.3934 56.6804 38.2491 56.7438 38.0789 56.7438ZM38.5265 57.6572V63.2831H37.6018V57.6572H38.5265ZM39.7584 60.4496C39.7584 59.8755 39.873 59.3685 40.1061 58.936C40.3354 58.4998 40.6535 58.1642 41.0567 57.9256C41.46 57.687 41.9112 57.5677 42.4069 57.5677C42.9026 57.5677 43.3206 57.6721 43.6794 57.8846C44.0382 58.0971 44.3083 58.3618 44.4821 58.6862V57.6609H45.418V63.2868H44.4821V62.2392C44.2972 62.5673 44.0272 62.8394 43.6609 63.0557C43.2984 63.2719 42.8767 63.38 42.3921 63.38C41.9075 63.38 41.4489 63.257 41.0493 63.0109C40.6498 62.7649 40.3317 62.4181 40.1024 61.9745C39.873 61.5308 39.7547 61.0238 39.7547 60.4571L39.7584 60.4496ZM44.4858 60.4608C44.4858 60.0358 44.4008 59.6667 44.2306 59.3535C44.0604 59.0404 43.8311 58.798 43.5426 58.6303C43.254 58.4625 42.9359 58.3805 42.5919 58.3805C42.2479 58.3805 41.9297 58.4625 41.6449 58.6265C41.3601 58.7906 41.1307 59.0292 40.9643 59.3461C40.7941 59.6593 40.709 60.0321 40.709 60.4534C40.709 60.8747 40.7941 61.2587 40.9643 61.5756C41.1344 61.8925 41.3601 62.1385 41.6449 62.3063C41.9297 62.4741 42.2442 62.5561 42.5919 62.5561C42.9396 62.5561 43.254 62.4741 43.5426 62.3063C43.8311 62.1385 44.0604 61.8962 44.2306 61.5756C44.4008 61.2587 44.4858 60.8858 44.4858 60.4608Z" fill={ darkMode ? "white" : "#6F6F6E"}/>
<path d="M31.1652 48.8433H23.3453C21.307 49.9841 18.8693 50.5582 16.0358 50.5582C11.6153 50.5582 7.83847 48.9887 4.7016 45.8458C1.56843 42.7029 0 38.9039 0 34.4524C0 30.0009 1.56843 26.2391 4.7016 23.1745C7.83847 20.11 11.6153 18.5777 16.0358 18.5777C18.7176 18.5777 21.1554 19.1854 23.3453 20.4045V0H31.1652V48.8433ZM16.0358 26.2279C13.7682 26.2279 11.8409 27.0258 10.2577 28.6252C8.67078 30.2246 7.87916 32.167 7.87916 34.4486C7.87916 36.7303 8.67078 38.628 10.2577 40.246C11.8446 41.864 13.7719 42.6731 16.0358 42.6731C18.2996 42.6731 20.2195 41.8454 21.5586 40.1901C22.8977 38.5348 23.5709 36.6222 23.5709 34.4524C23.5709 32.2826 22.9014 30.37 21.5586 28.7147C20.2158 27.0593 18.3773 26.2317 16.0358 26.2317V26.2279Z" fill={darkMode ? "#FFFFFF" : "#111C9D"}/>
<path d="M45.4217 48.9704H37.5463V20.416H45.4217V48.9704Z" fill={darkMode ? "#FFFFFF" : "#111C9D"}/>
<path fillRule="evenodd" clipRule="evenodd" d="M59.8188 57.9811L55.1615 64.3787C57.7694 65.8998 60.6992 66.6603 63.9507 66.6603C68.8225 66.6603 72.9877 64.8894 76.4464 61.3514C79.9014 57.8096 81.6326 53.5855 81.6326 48.6717V19.2599H73.9273C71.8114 18.1191 69.3366 17.5449 66.5031 17.5449C62.1196 17.5449 58.3724 19.107 55.254 22.2276C52.1393 25.3481 50.5783 29.1359 50.5783 33.5911C50.5783 38.0463 52.1356 41.7671 55.254 44.869C58.3687 47.9708 62.1196 49.5218 66.5031 49.5218C69.0333 49.5218 71.471 48.8731 73.8126 47.5794V48.6643C73.8126 51.4045 72.8397 53.7868 70.894 55.8038C68.9482 57.8208 66.6363 58.8311 63.9544 58.8311C62.434 58.8311 61.058 58.5478 59.8225 57.9773L59.8188 57.9811ZM66.4994 25.4301C64.2688 25.4301 62.3638 26.2279 60.7768 27.8273C59.1899 29.4267 58.3983 31.3505 58.3983 33.5949C58.3983 35.8392 59.1899 37.8226 60.7768 39.4183C62.3638 41.0177 64.2725 41.8156 66.4994 41.8156C68.841 41.8156 70.6831 40.9879 72.0222 39.3326C73.365 37.6772 74.0345 35.7647 74.0345 33.5949C74.0345 31.425 73.365 29.5199 72.0222 27.8832C70.6794 26.2466 68.841 25.4264 66.4994 25.4264V25.4301Z" fill={darkMode ? "#FFFFFF" : "#111C9D"}/>
<path d="M90.4661 53.373C93.2441 56.7769 96.7176 59.4687 100.591 61.3141C104.449 63.1484 108.718 64.1364 113.112 64.1364C117.507 64.1364 121.776 63.1521 125.63 61.3141C129.503 59.4724 132.977 56.7769 135.755 53.373L140.996 57.7201C137.56 61.9256 133.287 65.2474 128.53 67.5104C123.755 69.7809 118.498 71.0037 113.108 71.0037C107.719 71.0037 102.462 69.7846 97.6868 67.5104C92.9297 65.2474 88.6535 61.9256 85.2207 57.7201L90.4624 53.373H90.4661Z" fill={darkMode ? "#FFFFFF" : "#111C9D"}/>
<path fillRule="evenodd" clipRule="evenodd" d="M37.5463 3.57227V11.7967H45.4217V3.57227H37.5463Z" fill={darkMode ? "#FFFFFF" : "#56C3BD"}/>
<path d="M119.985 48.8435H112.165C110.127 49.9844 107.708 50.5548 104.911 50.5548C100.528 50.5548 96.7916 48.9927 93.6918 45.8722C90.5956 42.7516 89.0457 38.9824 89.0457 34.5645C89.0457 30.1466 90.5956 26.3811 93.6918 23.2568C96.7879 20.1326 100.528 18.5742 104.911 18.5742C107.708 18.5742 110.123 19.1446 112.165 20.2855H119.985V48.8398V48.8435ZM104.915 26.3997C102.684 26.3997 100.787 27.1976 99.222 28.797C97.6535 30.3964 96.8693 32.3201 96.8693 34.5645C96.8693 36.8089 97.6535 38.7326 99.222 40.332C100.79 41.9314 102.688 42.7293 104.915 42.7293C107.142 42.7293 109.043 41.9016 110.382 40.2463C111.725 38.591 112.395 36.697 112.395 34.5645C112.395 32.432 111.725 30.538 110.382 28.8827C109.039 27.2274 107.22 26.3997 104.915 26.3997Z" fill={darkMode ? "#FFFFFF" : "#56C3BD"}/>
<path d="M134.416 48.8434H126.536V20.2891H134.416V48.8434Z" fill={darkMode ? "#FFFFFF" : "#56C3BD"}/>
<path fillRule="evenodd" clipRule="evenodd" d="M126.536 3.57227V11.7967H134.416V3.57227H126.536Z" fill={darkMode ? "#FFFFFF" : "#111C9D"}/>
</g>
<defs>
<clipPath id="clip0_3397_28871">
<rect width="141" height="71" fill="white"/>
</clipPath>
</defs>
</svg>

  );
};
