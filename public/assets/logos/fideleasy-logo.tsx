export function FidelEasyLogo({
  favicon,
  darkMode,
}: {
  favicon?: boolean;
  darkMode?: boolean;
}) {
  return favicon ? (
    <svg
      width='32'
      height='32'
      viewBox='0 0 45 45'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='32' height='32' rx='0.636359' fill='#E4B62E' />
      <path
        d='M8.88867 8.69548V14.8777L10.1251 14.1504V10.2229L28.5265 33.1336V39.6068L36.0179 35.2429V28.7697L16.3074 4.25879L8.88867 8.69548Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M13.6163 25.133V21.2055L25.6899 36.3339L19.8713 39.6068V33.1336L13.6163 25.133Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M13.6163 38.9522L11.2888 35.8975V37.8612L13.6163 40.916L18.6348 37.8612L13.6163 31.679V38.9522Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
    </svg>
  ) : (
    <svg
      width='176'
      height='45'
      viewBox='0 0 176 45'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='45' height='45' rx='0.636359' fill='#E4B62E' />
      <path
        d='M8.88867 8.69548V14.8777L10.1251 14.1504V10.2229L28.5265 33.1336V39.6068L36.0179 35.2429V28.7697L16.3074 4.25879L8.88867 8.69548Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M13.6163 25.133V21.2055L25.6899 36.3339L19.8713 39.6068V33.1336L13.6163 25.133Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M13.6163 38.9522L11.2888 35.8975V37.8612L13.6163 40.916L18.6348 37.8612L13.6163 31.679V38.9522Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M159.348 40.9087L164.322 30.7285L158.229 18.1172H163.004L165.64 23.6885C165.872 24.1949 166.071 24.6592 166.237 25.0813C166.42 25.4865 166.569 25.8832 166.685 26.2715C166.818 25.8832 166.975 25.4865 167.157 25.0813C167.34 24.6592 167.555 24.1949 167.804 23.6885L170.49 18.1172H175.092L163.999 40.9087H159.348Z'
        fill='#E4B62E'
      />
      <path
        d='M152.153 33.8187C151.141 33.8187 150.113 33.6498 149.069 33.3122C148.041 32.9745 147.237 32.5187 146.656 31.9447L147.726 27.7916C148.207 28.4669 148.87 29.0578 149.715 29.5642C150.578 30.0707 151.465 30.3239 152.377 30.3239C152.99 30.3239 153.43 30.2058 153.695 29.9694C153.977 29.7331 154.118 29.4461 154.118 29.1084C154.118 28.7201 153.977 28.4415 153.695 28.2727C153.413 28.087 153.123 27.9351 152.824 27.8169L150.735 27.0065C150.271 26.8208 149.74 26.5676 149.143 26.2468C148.547 25.926 148.024 25.4702 147.577 24.8793C147.145 24.2884 146.93 23.5118 146.93 22.5495C146.93 21.6716 147.145 20.8612 147.577 20.1184C148.008 19.3756 148.629 18.7762 149.442 18.3204C150.271 17.8477 151.266 17.6113 152.426 17.6113C153.504 17.6113 154.507 17.797 155.436 18.1685C156.381 18.5399 157.069 18.9535 157.5 19.4093L156.505 23.4105C155.942 22.6677 155.27 22.1021 154.491 21.7138C153.728 21.3086 152.999 21.106 152.302 21.106C151.772 21.106 151.382 21.2158 151.133 21.4352C150.884 21.6547 150.76 21.908 150.76 22.195C150.76 22.4144 150.843 22.6339 151.009 22.8534C151.191 23.056 151.49 23.2417 151.904 23.4105L153.844 24.1449C154.408 24.3475 155.005 24.6176 155.635 24.9553C156.282 25.2929 156.829 25.7656 157.276 26.3734C157.724 26.9643 157.948 27.7831 157.948 28.8298C157.948 30.2986 157.409 31.4973 156.331 32.4258C155.27 33.3544 153.877 33.8187 152.153 33.8187Z'
        fill='#E4B62E'
      />
      <path
        d='M136.286 33.7166C134.91 33.7166 133.658 33.3537 132.53 32.6277C131.403 31.9018 130.499 30.9395 129.819 29.7408C129.156 28.5252 128.824 27.1831 128.824 25.7143C128.824 24.2455 129.156 22.9033 129.819 21.6878C130.499 20.4722 131.403 19.5099 132.53 18.8008C133.658 18.0749 134.91 17.7119 136.286 17.7119C137.247 17.7119 138.126 17.8892 138.922 18.2437C139.718 18.5814 140.398 19.0541 140.962 19.6619V18.1171H144.692V33.3115H140.962V31.7667C140.398 32.3745 139.718 32.8556 138.922 33.2102C138.126 33.5478 137.247 33.7166 136.286 33.7166ZM136.808 29.8421C137.538 29.8421 138.201 29.6564 138.798 29.285C139.395 28.9135 139.867 28.4155 140.215 27.7908C140.58 27.1662 140.763 26.474 140.763 25.7143C140.763 24.9546 140.58 24.2624 140.215 23.6377C139.867 23.0131 139.395 22.515 138.798 22.1436C138.201 21.7722 137.538 21.5865 136.808 21.5865C136.078 21.5865 135.415 21.7722 134.818 22.1436C134.221 22.515 133.741 23.0131 133.376 23.6377C133.028 24.2624 132.853 24.9546 132.853 25.7143C132.853 26.474 133.028 27.1662 133.376 27.7908C133.741 28.4155 134.221 28.9135 134.818 29.285C135.415 29.6564 136.078 29.8421 136.808 29.8421Z'
        fill='#E4B62E'
      />
      <path
        d='M120.848 33.8187C119.174 33.8187 117.698 33.4641 116.421 32.755C115.145 32.046 114.15 31.0752 113.437 29.8428C112.724 28.5935 112.367 27.1669 112.367 25.563C112.367 24.0267 112.715 22.6677 113.412 21.4859C114.108 20.2872 115.045 19.3418 116.222 18.6496C117.416 17.9574 118.734 17.6113 120.177 17.6113C121.653 17.6113 122.971 17.9574 124.132 18.6496C125.292 19.3249 126.204 20.2535 126.867 21.4352C127.547 22.617 127.887 23.9592 127.887 25.4618V27.0065H118.784C118.37 27.0065 117.972 26.9812 117.59 26.9305C117.209 26.8799 116.828 26.8208 116.446 26.7533C116.645 27.7662 117.134 28.5935 117.914 29.235C118.709 29.8597 119.721 30.172 120.948 30.172C121.893 30.172 122.739 29.9779 123.485 29.5896C124.248 29.2013 124.869 28.7201 125.35 28.1461L126.295 32.0966C125.549 32.6706 124.704 33.1011 123.758 33.3881C122.83 33.6752 121.86 33.8187 120.848 33.8187ZM116.347 24.0943C117.176 23.9085 117.988 23.8157 118.784 23.8157H121.421C121.885 23.8157 122.324 23.8326 122.739 23.8663C123.153 23.9001 123.543 23.9592 123.908 24.0436C123.659 23.1319 123.203 22.4144 122.54 21.8911C121.893 21.3677 121.089 21.106 120.127 21.106C119.215 21.106 118.419 21.3762 117.739 21.9164C117.076 22.4398 116.612 23.1657 116.347 24.0943Z'
        fill='#E4B62E'
      />
      <path
        d='M106.184 33.311V9H110.511V33.311H106.184Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M97.4149 33.8187C95.7402 33.8187 94.2644 33.4641 92.9877 32.755C91.7109 32.046 90.7161 31.0752 90.0031 29.8428C89.2901 28.5935 88.9336 27.1669 88.9336 25.563C88.9336 24.0267 89.2818 22.6677 89.9782 21.4859C90.6746 20.2872 91.6115 19.3418 92.7887 18.6496C93.9826 17.9574 95.3008 17.6113 96.7433 17.6113C98.219 17.6113 99.5372 17.9574 100.698 18.6496C101.859 19.3249 102.771 20.2535 103.434 21.4352C104.114 22.617 104.454 23.9592 104.454 25.4618V27.0065H95.3505C94.936 27.0065 94.538 26.9812 94.1567 26.9305C93.7753 26.8799 93.3939 26.8208 93.0126 26.7533C93.2115 27.7662 93.7007 28.5935 94.48 29.235C95.2759 29.8597 96.2873 30.172 97.5143 30.172C98.4595 30.172 99.3051 29.9779 100.051 29.5896C100.814 29.2013 101.436 28.7201 101.917 28.1461L102.862 32.0966C102.116 32.6706 101.27 33.1011 100.325 33.3881C99.3963 33.6752 98.4263 33.8187 97.4149 33.8187ZM92.9131 24.0943C93.7421 23.9085 94.5546 23.8157 95.3505 23.8157H97.9869C98.4512 23.8157 98.8906 23.8326 99.3051 23.8663C99.7196 23.9001 100.109 23.9592 100.474 24.0436C100.225 23.1319 99.7694 22.4144 99.1061 21.8911C98.4595 21.3677 97.6553 21.106 96.6936 21.106C95.7816 21.106 94.9857 21.3762 94.3059 21.9164C93.6426 22.4398 93.1784 23.1657 92.9131 24.0943Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M78.7383 33.7162C77.3455 33.7162 76.0854 33.3532 74.9578 32.6272C73.8303 31.9013 72.9267 30.939 72.2468 29.7403C71.5836 28.5248 71.252 27.1826 71.252 25.7138C71.252 24.245 71.5836 22.9028 72.2468 21.6873C72.9267 20.4717 73.8303 19.5094 74.9578 18.8004C76.0854 18.0744 77.3455 17.7114 78.7383 17.7114C79.5342 17.7114 80.2721 17.8296 80.9519 18.066C81.6318 18.3023 82.2453 18.64 82.7924 19.0789V9H87.1201V33.311H83.3894V31.8169C82.8256 32.4078 82.1458 32.872 81.3499 33.2097C80.554 33.5473 79.6835 33.7162 78.7383 33.7162ZM79.2358 29.8416C79.9654 29.8416 80.6286 29.6559 81.2255 29.2845C81.8224 28.9131 82.295 28.415 82.6432 27.7904C83.008 27.1657 83.1904 26.4735 83.1904 25.7138C83.1904 24.9541 83.008 24.2619 82.6432 23.6372C82.295 23.0126 81.8224 22.5145 81.2255 22.1431C80.6286 21.7717 79.9654 21.586 79.2358 21.586C78.5062 21.586 77.843 21.7717 77.246 22.1431C76.6491 22.5145 76.1683 23.0126 75.8035 23.6372C75.4553 24.2619 75.2812 24.9541 75.2812 25.7138C75.2812 26.4735 75.4553 27.1657 75.8035 27.7904C76.1683 28.415 76.6491 28.9131 77.246 29.2845C77.843 29.6559 78.5062 29.8416 79.2358 29.8416Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M65.0691 33.3099V18.1156H69.3965V33.3099H65.0691ZM67.2328 15.9124C66.5862 15.9124 66.0391 15.6845 65.5914 15.2287C65.1437 14.7728 64.9199 14.2157 64.9199 13.5573C64.9199 12.882 65.1437 12.3248 65.5914 11.8859C66.0391 11.4301 66.5862 11.2021 67.2328 11.2021C67.896 11.2021 68.4431 11.4301 68.8742 11.8859C69.3219 12.3248 69.5457 12.882 69.5457 13.5573C69.5457 14.2157 69.3219 14.7728 68.8742 15.2287C68.4431 15.6845 67.896 15.9124 67.2328 15.9124Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
      <path
        d='M51.5469 33.3111V10.5195H63.4853V14.3181H55.8746V17.9648H62.7392V21.7634H55.8746V33.3111H51.5469Z'
        fill={darkMode ? '#fff' : '#063067'}
      />
    </svg>
  );
}
