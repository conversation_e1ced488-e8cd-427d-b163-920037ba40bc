.toast-container {
  @apply z-[1090] max-w-full pointer-events-none;
}

.toast {
  @apply bg-white border border-solid border-defaultborder shadow-sm rounded-md;
}
.toast .toast-header {
  @apply bg-light py-[0.375rem] px-3 flex items-center;
}
.toast .toast-header img {
  @apply w-[1.25rem] h-[1.25rem];
}
.toast .toast-header {
  @apply border-b border-solid border-defaultborder;
}
.toast .toast-header .btn-close {
  @apply me-[0.125rem] p-3;
}
.toast .toast-body {
  @apply text-[0.8rem] p-3;
}
.toast .btn-close {
  @apply text-[0.563rem];
}
.toast.colored-toast {
  @apply border-0 backdrop-blur-[20px] !important;
}
.toast.colored-toast .btn-close {
  @apply invert-[1];
}
.toast.colored-toast .toast-header {
  @apply border-b border-solid border-black/10;
}

.bd-example-toasts {
  @apply min-h-[15rem];
}

/*# sourceMappingURL=toast.css.map */
