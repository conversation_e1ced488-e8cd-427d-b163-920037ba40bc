.svg-color-1-text [class*="color-1"] {
  fill: currentColor;
}
.svg-color-2-text [class*="color-2"] {
  fill: currentColor;
}
svg[viewBox="0 0 24 24"] {
  display: inline-flex;
  overflow: visible;
}

.form-control:focus {
  border-color: rgb(var(--primary) / 0.8) !important;
}

.animated-progress-bar:after,
.animated-progress-bar:before {
  content: "";
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
  height: 200%;
  width: 0.5rem;
  transform: rotate(30deg);
  top: -50%;
  animation: animated-progress-bar-shine 1s linear infinite;
  left: 0px;
}

.animated-progress-bar:before {
  animation-duration: 0.7s;
}

.animated-progress-bar {
  position: relative;
  overflow: hidden;
}

@keyframes animated-progress-bar-shine {
  0% {
    left: 0%;
  }
  100% {
    left: 110%;
  }
}

.custom-toggle-switch > label::after {
  border: 1px solid rgb(var(--input-border) / 60%);
}

*::-webkit-scrollbar,
*::-webkit-scrollbar-thumb {
  height: 8px;
  width: 8px;
}

teal
indigo
slate
orange
red
gray

.apex-series-1-stroke-current-color .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-current-color .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-current-color .apexcharts-series path:nth-of-type(3) {
  stroke: currentColor !important;
}
.apex-series-1-fill-current-color .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-current-color .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-current-color .apexcharts-series path:nth-of-type(3) {
  fill: currentColor !important;
}


.apex-series-1-stroke-primary .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-primary .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-primary .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--primary) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-primary .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-primary .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-primary .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--primary) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-secondary .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-secondary .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-secondary .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--secondary) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-secondary .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-secondary .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-secondary .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--secondary) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-light .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-light .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-light .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--light) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-light .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-light .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-light .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--light) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-dark .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-dark .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-dark .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--dark) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-dark .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-dark .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-dark .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--dark) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-warning .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-warning .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-warning .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--warning) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-warning .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-warning .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-warning .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--warning) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-info .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-info .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-info .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--info) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-info .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-info .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-info .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--info) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-success .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-success .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-success .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--success) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-success .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-success .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-success .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--success) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-danger .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-danger .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-danger .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--danger) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-danger .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-danger .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-danger .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--danger) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-pink .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-pink .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-pink .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--pink) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-pink .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-pink .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-pink .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--pink) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-red .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-red .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-red .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--red) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-red .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-red .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-red .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--red) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-orange .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-orange .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-orange .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--orange) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-orange .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-orange .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-orange .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--orange) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-yellow .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-yellow .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-yellow .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--yellow) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-yellow .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-yellow .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-yellow .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--yellow) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-green .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-green .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-green .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--green) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-green .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-green .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-green .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--green) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-teal .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-teal .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-teal .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--teal) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-teal .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-teal .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-teal .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--teal) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-cyan .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-cyan .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-cyan .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--cyan) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-cyan .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-cyan .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-cyan .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--cyan) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-blue .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-blue .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-blue .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--blue) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-blue .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-blue .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-blue .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--blue) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-purple .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-purple .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-purple .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--purple) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-purple .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-purple .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-purple .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--purple) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-indigo .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-indigo .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-indigo .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--indigo) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-indigo .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-indigo .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-indigo .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--indigo) / var(--apex-fill-opacity)) !important;
}

.apex-series-1-stroke-gray .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-gray .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-gray .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
  stroke: rgb(var(--gray) / var(--apex-stroke-opacity)) !important;
}
.apex-series-1-fill-gray .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-gray .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-gray .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
  fill: rgba(var(--gray) / var(--apex-fill-opacity)) !important;
}



.apex-series-1-stroke-opacity-0 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-0 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-0 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.0;
}
.apex-series-1-fill-opacity-0 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-0 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-0 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.0;
}
.apex-series-1-stroke-opacity-25 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-25 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-25 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.25;
}
.apex-series-1-fill-opacity-25 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-25 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-25 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.25;
}
.apex-series-1-stroke-opacity-50 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-50 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-50 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.50;
}
.apex-series-1-fill-opacity-50 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-50 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-50 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.50;
}
.apex-series-1-stroke-opacity-60 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-60 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-60 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.60;
}
.apex-series-1-fill-opacity-60 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-60 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-60 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.60;
}
.apex-series-1-stroke-opacity-75 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-75 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-75 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.75;
}
.apex-series-1-fill-opacity-75 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-75 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-75 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.75;
}
.apex-series-1-stroke-opacity-80 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-80 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-80 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.80;
}
.apex-series-1-fill-opacity-80 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-80 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-80 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.80;
}
.apex-series-1-stroke-opacity-85 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-85 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-85 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.85;
}
.apex-series-1-fill-opacity-85 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-85 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-85 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.85;
}
.apex-series-1-stroke-opacity-90 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-90 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-90 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.90;
}
.apex-series-1-fill-opacity-90 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-90 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-90 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.90;
}
.apex-series-1-stroke-opacity-95 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-95 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-95 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 0.95;
}
.apex-series-1-fill-opacity-95 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-95 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-95 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 0.95;
}
.apex-series-1-stroke-opacity-100 .apexcharts-series path:nth-of-type(1),
.apex-series-2-stroke-opacity-100 .apexcharts-series path:nth-of-type(2),
.apex-series-3-stroke-opacity-100 .apexcharts-series path:nth-of-type(3) {
  --apex-stroke-opacity: 1;
}
.apex-series-1-fill-opacity-100 .apexcharts-series path:nth-of-type(1),
.apex-series-2-fill-opacity-100 .apexcharts-series path:nth-of-type(2),
.apex-series-3-fill-opacity-100 .apexcharts-series path:nth-of-type(3) {
  --apex-fill-opacity: 1;
}