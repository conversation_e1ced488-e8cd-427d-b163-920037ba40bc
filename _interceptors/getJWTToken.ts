'use server'

import { cookies } from "next/headers";

export async function getAccessToken(): Promise<string> {
    'use server'
    const cookieStore = await cookies();
  
    const token = cookieStore.get('digai.accessToken')?.value ?? '';

    return token;
}

export async function getRefreshToken(): Promise<string> {
    'use server'
    const cookieStore = await cookies();
  
    const token = cookieStore.get('digai.refreshToken')?.value ?? '';

    return token;
}