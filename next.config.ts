import type { NextConfig } from 'next';

const isProd = process.env.NODE_ENV === 'production';

const nextConfig: NextConfig = {
  webpack(config, _options) {
    const fileLoaderRule = config.module.rules.find((rule: any) =>
      rule.test?.test?.('.svg'),
    );

    config.module.rules.push(
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/,
      },
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] },
        use: ['@svgr/webpack'],
      },
    );

    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
  deploymentId: process.env.COMMIT_HASH
    ? `TRNSCD-${process.env.COMMIT_HASH.toUpperCase()}`
    : 'TRNSCD',
  reactStrictMode: true,
  trailingSlash: true,
  basePath: '',
  assetPrefix: isProd ? process.env.ASSET_PREFIX || '' : '',
  images: {
    loader: 'imgix',
    path: isProd ? process.env.ASSET_PATH || '/' : '/',
  },
  // typescript: {
  //   ignoreBuildErrors: true, // Temporary for debugging
  // },
  i18n: {
    locales: ['pt-BR', 'en'],
    defaultLocale: 'pt-BR',
    localeDetection: false,
  },
  env: {
    NEXT_PUBLIC_COMMIT_HASH: process.env.NEXT_PUBLIC_COMMIT_HASH || '',
    COMMIT_HASH: process.env.COMMIT_HASH || '',
  },
};

export default nextConfig;
