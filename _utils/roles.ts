import jwt from 'jsonwebtoken';

interface RolesInPages {
  [key: string]: { account: string[]; userInAccount: string[] };
}

// TODO - define the roles for each page
const rolesInPages: RolesInPages = {
  '/portfolio/imports': {
    account: ['BASIC'],
    userInAccount: ['ADMIN'],
  },
  '/portfolio/imports/create': {
    account: ['BASIC'],
    userInAccount: ['ADMIN'],
  },
  '/portfolio/performance': {
    account: ['BASIC'],
    userInAccount: ['ADMIN'],
  },
  '/negotiation': {
    account: ['BASIC'],
    userInAccount: ['ADMIN'],
  },
  '/integration': {
    account: ['BASIC'],
    userInAccount: ['ADMIN'],
  },
};

export function getJWTPayloadInfo(token: string): { accountRole: string; userRoleInAccount: string; accountId: string; userId: string } {
  const decoded = jwt.decode(token) as any;
  return { accountRole: decoded.accountRole, userRoleInAccount: decoded.roleInAccount, accountId: decoded.accountId, userId: decoded.userId };
}

export function validateAccountRoleAccess(role: string, path: string) {
  let redirectUser = false;
  for (const route in rolesInPages) {
    if (path.includes(route) && !rolesInPages[route].account.includes(role)) {
      redirectUser = true;
      break;
    }
  }
  return redirectUser;
}

export function validateUserInAccountRoleAccess(role: string, path: string) {
  let redirectUser = false;
  for (const route in rolesInPages) {
    if (path.includes(route) && !rolesInPages[route].userInAccount.includes(role)) {
      redirectUser = true;
      break;
    }
  }
  return redirectUser;
}
