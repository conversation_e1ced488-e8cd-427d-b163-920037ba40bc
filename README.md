# Project X / Backoffice Admin / CollectCash / Fideleasy / SalesZap / The Negotiator / Transcendence Front end / Dashboardi / Digaí

Visual Interface to manage the messaging workflow engine that fuels the 3 pillars of negotiation: Collect Cash, Fideleasy and SalesZap.

## Getting Started

### Front-end

- Have Node.js installed
- Have yarn (or npm, or pnpm, whatever) installed
- `cd` to this folder
- Install dependencies: `yarn install`
- Start the development server: `yarn dev`
- Open the url in your browser (if not auto)

### Integration

- Have docker installed
- Have Transcendence cloned
- Start the respective Transcendence service: `npm run local-infra:start; npm run start`
- Ensure that there is an `.env` file, with the correct local URL for Transcendence
- Wait for Transcendence to start
- Reload the tab your browser


## Third-Party Docs:

- [Next.js](https://nextjs.org/docs)
- [React](https://react.dev/)
- [Typescript](https://www.typescriptlang.org/docs/handbook/2/basic-types.html)
- [Tailwind](https://tailwindcss.com/docs/)
- [React Icons](https://react-icons.github.io/react-icons/)
- [TanStack Query](https://tanstack.com/query/v4/docs/framework/react/reference/useQuery)
- [Axios](https://axios-http.com/docs)
- [Zod](https://zod.dev/)
- [Zustand](https://zustand-demo.pmnd.rs/)
- [Day.js](https://day.js.org/)
- [Lodash](https://lodash.com/)
- [RC Field Form](https://github.com/react-component/field-form)
- [React Types CheatSheet](https://react-typescript-cheatsheet.netlify.app/docs/basic/getting-started/forms_and_events)
- [SVGR](https://react-svgr.com/docs/webpack/)
- [Ynex Template Admin](https://nextjs.spruko.com/tailwind/app/ynex-ts/preview/dashboards/crm/)
- [Preline](https://preline.co/)
- [Apex Charts](https://apexcharts.com/)

