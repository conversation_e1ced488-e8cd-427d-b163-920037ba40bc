'use client';
import { useState } from 'react';
import { connect } from 'react-redux';
import { ThemeChanger } from '@/shared/redux/action';
import PrelineScript from '@/app/PrelineScript';
import Backtotop from '@/shared/layout-components/backtotop/backtotop';
import Footer from '@/shared/layout-components/footer/footer';
import Header from '@/shared/layout-components/header/header';
import Sidebar from '@/shared/layout-components/sidebar/sidebar';
import Switcher from '@/shared/layout-components/switcher/switcher';

interface LayoutProps {
  children: React.ReactNode;
  theme: any;
  ThemeChanger: (theme: any) => void;
}

const Layout = ({ children, theme, ThemeChanger }: LayoutProps) => {
  const [MyclassName, setMyClass] = useState('');

  const Bodyclickk = () => {
    if (localStorage.getItem('ynexverticalstyles') === 'icontext') {
      setMyClass('');
    }
    if (window.innerWidth > 992) {
      if (theme.iconOverlay === 'open') {
        ThemeChanger({ ...theme, iconOverlay: '' });
      }
    }
  };

  return (
    <>
      <Switcher />
      <div className='page'>
        <Header />
        <Sidebar />
        <div className='content'>
          <div className='main-content' onClick={Bodyclickk}>
            {children}
          </div>
        </div>
        <Footer />
      </div>
      <Backtotop />
      <PrelineScript />
    </>
  );
};

const mapStateToProps = (state: any) => ({
  theme: state, 
});

export default connect(mapStateToProps, { ThemeChanger })(Layout);