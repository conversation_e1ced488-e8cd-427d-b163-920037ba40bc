import Link from "next/link";
import {
  getAllPortfolioItems,
  IPortfolioItem,
} from "@/shared/_services/portfolio-item.provider";
import {
  getAllPortfolios,
  Portfolio,
} from "@/shared/_services/portfolio.provider";
import { twMerge } from "tailwind-merge";
import {
  ColorPortfolioItemStatusEnum,
  IconPortfolioItemStatusEnum,
} from "@/shared/layout-components/icons";
import { formatDateSimple } from "@/shared/_utils/formatters/formatDate";
import { formatPhoneNumber } from "@/shared/_utils/formatters/formatNumber";
import { useQuery } from "@tanstack/react-query";
import { Pagination } from "antd";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

type Pagination = {
  current: number;
  pageSize: number;
  total?: number;
  totalPages?: number;
};
interface NegociationsTableProps {
  title: string;
  waitingBusinessUserResponseFilter?: string;
  hasInteractionFilter?: string;
}

const NegociationsTable = ({
  title,
  waitingBusinessUserResponseFilter,
  hasInteractionFilter,
}: NegociationsTableProps) => {
  const [pagination, setPagination] = useState<Pagination>({
    current: 1,
    pageSize: 10,
  });
  const searchParams = useSearchParams();
  const searchPortfolioId = searchParams.get("portfolioId") || "";

  const searchObject: Record<string, any> = {};
  if (waitingBusinessUserResponseFilter === "true") {
    searchObject.waitingBusinessUserResponse = true;
  }
  if (hasInteractionFilter === "true") {
    searchObject.lastInteraction = { not: null };
  }

  const { data: negotiationsData, isLoading: negotiationsLoading } = useQuery({
    queryKey: [
      "negotiations",
      searchObject,
      pagination.current,
      pagination.pageSize,
    ],
    queryFn: async () => {
      const portfolios = await getAllPortfolios();
      return getAllPortfolioItems(
        searchObject,
        {
          page: pagination.current,
          limit: pagination.pageSize,
        },
        portfolios
      );
    },
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (negotiationsData) {
      setPagination((prev) => ({
        ...prev,
        total: negotiationsData.total,
        totalPages: negotiationsData.totalPages,
      }));
    }
  }, [negotiationsData]);

  const handlePagination = (page: number, pageSize: number) => {
    setPagination((prev) => ({
      ...prev,
      current: page,
      pageSize,
    }));
  };

  const negotiations = negotiationsData?.items || [];

  const statusOptions: { key: string; label: string; value: string }[] = [
    {
      key: "PENDING",
      label: "Pendente",
      value: "PENDING",
    },
    {
      key: "IN_PROGRESS",
      label: "Em Andamento",
      value: "IN_PROGRESS",
    },
    {
      key: "PAUSED",
      label: "Pausado",
      value: "PAUSED",
    },
    {
      key: "SUCCEED",
      label: "Concluído",
      value: "SUCCEED",
    },
    {
      key: "FAILED",
      label: "Falhou",
      value: "FAILED",
    },
    {
      key: "CANCELLED",
      label: "Cancelado",
      value: "CANCELLED",
    },
    {
      key: "UNLINKED",
      label: "Desvinculado",
      value: "UNLINKED",
    },
    {
      key: "IDLE",
      label: "Inativo",
      value: "IDLE",
    },
    {
      key: "FOLLOWED_UP",
      label: "Follow up",
      value: "FOLLOWED_UP",
    },
    {
      key: "SCHEDULED_FOLLOW_UP",
      label: "Follow up agendado",
      value: "SCHEDULED_FOLLOW_UP",
    },
    {
      key: "OPTED_OUT",
      label: "Não perturbe",
      value: "OPTED_OUT",
    },
    {
      key: "FINISHED",
      label: "Finalizado",
      value: "FINISHED",
    },
  ];

  return (
    <div className="xxl:col-span-12 xl:col-span-12 md:col-span-12 col-span-12 pt-5">
      <div className="box">
        <div className="box-header sm:flex block">
          <div className="box-title">{title}</div>
        </div>
        <div className="box-body !p-0">
          <div className="tab-content !p-0">
            <div
              className="tab-pane show active !p-0 !border-0"
              id="Active"
              aria-labelledby="active-item"
              role="tabpanel"
            >
              <div className="table-responsive">
                <table className="table whitespace-nowrap table-hover min-w-full">
                  <thead>
                    <tr>
                      <th
                        scope="col"
                        className="text-start"
                        style={{ paddingLeft: "25px" }}
                      >
                        Telefone
                      </th>
                      <th
                        scope="col"
                        className="text-start"
                        style={{ paddingLeft: "25px" }}
                      >
                        Portfólio
                      </th>
                      <th scope="col" className="text-start">
                        Última Mensagem Recebida
                      </th>
                      <th scope="col" className="text-start">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {negotiationsLoading && (
                      <tr className="border-b border-defaultborder">
                        <td className="!p-0" colSpan={6}>
                          <p className="p-3 w-full h-full block leading-none text-center pt-6 pb-12 text-base">
                            Carregando negociações...
                          </p>
                        </td>
                      </tr>
                    )}
                    {negotiations.length > 0 &&
                      negotiations.map((negItem) => {
                        const IconPortfItemStatus =
                          IconPortfolioItemStatusEnum[negItem.currentStatus];
                        const statusColor =
                          ColorPortfolioItemStatusEnum[negItem.currentStatus];
                        return (
                          <tr
                            key={negItem.id}
                            className="border-t border-inherit border-solid hover:bg-gray-100 dark:hover:bg-light dark:border-defaultborder/10"
                          >
                            <td>
                              <Link
                                className="p-3 w-full h-full block hover:underline cursor-pointer hover:text-secondary leading-none"
                                href={`/negotiation/${negItem.id}`}
                              >
                                {formatPhoneNumber(negItem.phoneNumber)}
                              </Link>
                            </td>
                            <td className="text-[#8c9097] dark:text-white/50">
                              {negItem.portfolioId && (
                                <Link
                                  className="p-3 w-full h-full block hover:underline cursor-pointer hover:text-primary leading-none"
                                  href={`/portfolio/performance/${negItem.portfolioId}`}
                                >
                                  {negItem.portfolioName}
                                </Link>
                              )}
                            </td>
                            <td>
                              {formatDateSimple(
                                negItem.lastInteraction,
                                "both"
                              )}
                            </td>
                            <td className="!py-1">
                              <div className="flex items-center gap-2">
                                <div
                                  className={twMerge(
                                    "avatar avatar-md avatar-rounded",
                                    statusColor?.text,
                                    statusColor?.bg
                                  )}
                                >
                                  {IconPortfItemStatus && (
                                    <IconPortfItemStatus />
                                  )}
                                </div>
                                <span
                                  className={twMerge(
                                    "capitalize leading-none text-sm",
                                    statusColor?.text
                                  )}
                                >
                                  {
                                    statusOptions.find(
                                      (item) =>
                                        item.value === negItem.currentStatus
                                    )?.label
                                  }
                                </span>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div className="box-footer">
          <Pagination
            align="center"
            current={pagination?.current}
            total={pagination?.total}
            pageSize={pagination?.pageSize}
            showSizeChanger
            onChange={(page, size) => handlePagination(page, size)}
            style={{ marginTop: "16px", marginBottom: "16px" }}
          />
        </div>
      </div>
    </div>
  );
};

export default NegociationsTable;
