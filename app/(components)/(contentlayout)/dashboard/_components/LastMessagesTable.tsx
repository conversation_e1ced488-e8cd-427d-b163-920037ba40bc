import Link from 'next/link'
import React from 'react'

const LastMessagesTable = () => {
  return (
   <>
    <div className="grid grid-cols-12 gap-x-6">
        <div className="lg:col-span-6 sm:col-span-12 md:col-span-6 xxl:col-span-3 xl:col-span-4 col-span-12">
          <div className="box">
            <div className="box-header justify-between">
              <div className="box-title">Últimas Mensagens Recebidas</div>
            </div>
            <div className="box-body mt-0 latest-timeline" id="latest-timeline">
              <ul className="timeline-main mb-0 list-unstyled">
                <li>
                  <div className="featured_icon1 featured-danger"></div>
                </li>
                <li className="mt-0 activity">
                  <Link href="#!" scroll={false} className="text-[0.75rem]">
                    <p className="mb-0">
                      <span className="font-semibold">+55 11 99999-9999</span>
                      {": "}
                      <span className="ms-2 text-[0.75rem]">
                        Estou interessado em um acordo de 12 parcelas.
                      </span>
                    </p>
                  </Link>
                  <small className="text-[#8c9097] dark:text-white/50 mt-0 mb-0 text-[0 .625rem]">
                    12 mins ago.
                  </small>
                </li>
                <li>
                  <div className="featured_icon1 featured-success"> </div>
                </li>
                <li className="mt-0 activity">
                  <Link href="#!" scroll={false} className="text-[0.75rem]">
                    <p className="mb-0">
                      <span className="font-semibold">+55 21 98888-8888</span>
                      {": "}
                      <span>
                        Estou sem condições de pagar agora, mas tenta no mês que
                        vem.
                      </span>
                    </p>
                  </Link>
                  <small className="text-[#8c9097] dark:text-white/50 mt-0 mb-0 text-[0 .625rem]">
                    28 mins ago.
                  </small>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
   </>
  )
};

export default LastMessagesTable;
