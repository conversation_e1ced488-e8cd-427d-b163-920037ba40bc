import React, { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import {
  fetchPortfolioMetrics,
  fetchPortfolioItemsMetrics,
  fetchMessagesReceivedMetrics,
  fetchFirstMessagesSentMetrics,
  fetchAnswerMessagesSentMetrics,
  fetchPortfolioItemsOnlyAIMetrics,
} from "@/shared/_services/portfolio.provider";
import Pageheader from "@/shared/layout-components/page-header/pageheader";
import { useDateContext } from "../_context/DateContext";

const CardsMetrics = () => {
  const [onlyAIMetrics, setOnlyAIMetrics] = useState<number | "-">("-");

  const [firstMessagesSentMetrics, setFirstMessagesSentMetrics] = useState({
    totalFirstMessagesSent: { total: 0, dailyTotals: {} },
  });
  const [answerMessagesSentMetrics, setAnswerMessagesSentMetrics] = useState({
    totalAnswerMessagesSent: { total: 0, dailyTotals: {} },
  });

  const [messagesReceivedMetrics, setMessagesReceivedMetrics] = useState({
    totalMessagesReceived: { total: 0, dailyTotals: {} },
  });

  const { dateRange, setDateRange, grouping, setGrouping } = useDateContext();
  const [startDate, endDate] = dateRange;

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (dateRange && dateRange.length === 2 && grouping) {
      const [startDate, endDate] = dateRange;

      if (startDate && endDate) {
        const filtersToSave = {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          grouping: grouping,
        };

        localStorage.setItem(
          "cardsMetricsFilters",
          JSON.stringify(filtersToSave)
        );
      }
    }
  }, [dateRange, grouping]);

  useEffect(() => {
    const fetchStatistics = async () => {
      setIsLoading(true);
      try {
        if (startDate && endDate) {
          let adjustedEndDate = endDate;

          if (grouping === "monthly") {
            adjustedEndDate = new Date(
              endDate.getFullYear(),
              endDate.getMonth() + 1,
              0
            );
          }

          const formattedStartDate = startDate.toISOString().split("T")[0];
          const formattedEndDate = adjustedEndDate.toISOString().split("T")[0];

          const metrics = await fetchPortfolioMetrics(
            formattedStartDate,
            formattedEndDate
          );

          const itemsMetrics = await fetchPortfolioItemsMetrics(
            formattedStartDate,
            formattedEndDate
          );

          const messagesReceived = await fetchMessagesReceivedMetrics(
            formattedStartDate,
            formattedEndDate
          );
          setMessagesReceivedMetrics(messagesReceived);

          const firstMessagesSent = await fetchFirstMessagesSentMetrics(
            formattedStartDate,
            formattedEndDate
          );
          setFirstMessagesSentMetrics(firstMessagesSent);

          const answerMessagesSent = await fetchAnswerMessagesSentMetrics(
            formattedStartDate,
            formattedEndDate
          );
          setAnswerMessagesSentMetrics(answerMessagesSent);
          const onlyAiResp = await fetchPortfolioItemsOnlyAIMetrics(
            formattedStartDate,
            formattedEndDate
          );
          setOnlyAIMetrics(onlyAiResp);

          console.log(
            `Dados carregados para o período ${formattedStartDate} até ${formattedEndDate}:`,
            metrics,
            itemsMetrics,
            firstMessagesSent,
            answerMessagesSent,
            messagesReceived,
            onlyAiResp
          );
        } else {
          setFirstMessagesSentMetrics({
            totalFirstMessagesSent: { total: 0, dailyTotals: {} },
          });
          setAnswerMessagesSentMetrics({
            totalAnswerMessagesSent: { total: 0, dailyTotals: {} },
          });
          setMessagesReceivedMetrics({
            totalMessagesReceived: { total: 0, dailyTotals: {} },
          });
          setOnlyAIMetrics("-");
        }
      } catch (error) {
        console.error("Failed to fetch statistics:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStatistics();
  }, [startDate, endDate]);

  const handleGroupingChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newGrouping = e.target.value as "daily" | "monthly";
    setGrouping(newGrouping);
    setDateRange([null, null]);
    setErrorMessage(null);
  };

  const handleEndDateChange = (date: Date | null) => {
    if (startDate && date) {
      const diffInTime = date.getTime() - startDate.getTime();
      const diffInDays = diffInTime / (1000 * 3600 * 24);

      if (grouping === "daily" && diffInDays > 31) {
        setErrorMessage(
          "Para períodos acima de 31 dias, selecione o agrupamento mensal."
        );
        return;
      }
    }
    setErrorMessage(null);
    setDateRange([startDate, date]);
  };

  return (
    <div>
      <div className="flex flex-col mb-4">
        <div className="mb-2">
          <Pageheader currentpage="Performance Dashboard" />
        </div>

        <div className="flex flex-col items-start">
          <div className="flex flex-col mb-4">
            <label className="text-sm font-medium text-gray-700 mb-2">
              Selecione o agrupamento
            </label>
            <select
              className="form-control !w-36 mb-2 border-none"
              value={grouping}
              onChange={handleGroupingChange}
            >
              <option value="daily">Diário</option>
              <option value="monthly">Mensal</option>
            </select>
          </div>
          <div className="flex flex-col">
            <label className="text-sm font-medium text-gray-700 mb-2">
              Selecione o período
            </label>
            <div className="flex gap-2">
              <div className="flex flex-col">
                <DatePicker
                  selected={startDate}
                  onChange={(date) => setDateRange([date, endDate])}
                  selectsStart
                  startDate={startDate}
                  endDate={endDate}
                  className="form-control !w-36"
                  placeholderText={
                    grouping === "monthly" ? "Mês inicial" : "Data inicial"
                  }
                  dateFormat={grouping === "monthly" ? "MM/yyyy" : "dd/MM/yyyy"}
                  showMonthYearPicker={grouping === "monthly"}
                  isClearable={true}
                  popperClassName="!z-[9999]"
                />
              </div>
              <div className="flex flex-col">
                <DatePicker
                  selected={endDate}
                  onChange={handleEndDateChange}
                  selectsEnd
                  startDate={startDate}
                  endDate={endDate}
                  minDate={startDate ?? undefined}
                  maxDate={new Date()}
                  className="form-control !w-36"
                  placeholderText={
                    grouping === "monthly" ? "Mês final" : "Data final"
                  }
                  dateFormat={grouping === "monthly" ? "MM/yyyy" : "dd/MM/yyyy"}
                  showMonthYearPicker={grouping === "monthly"}
                  isClearable={true}
                  popperClassName="!z-[9999]"
                />
              </div>
            </div>
            {errorMessage && (
              <p className="text-red-500 text-sm mt-1">{errorMessage}</p>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-12 gap-x-6">
        {/* Atendimentos Iniciados */}
        <div className="xl:col-span-3 lg:col-span-6 md:col-span-6 sm:col-span-6 col-span-12">
          <div className="box">
            <div className="box-body" style={{ height: "140px" }}>
              <div className="grid grid-cols-12">
                <div className="col-span-6 pe-0">
                  <p className="mb-2" style={{ width: "50%" }}>
                    <span className="text-[1rem]">Atendimentos Iniciados</span>
                  </p>
                  <p className="mb-2 text-[0.75rem]">
                    <span className="text-[1.5625rem] font-semibold leading-none vertical-bottom mb-0">
                      {isLoading
                        ? "-"
                        : firstMessagesSentMetrics.totalFirstMessagesSent.total}
                    </span>
                    <span className="block text-[0.625rem] font-semibold text-[#8c9097] dark:text-white/50">
                      {startDate && endDate
                        ? grouping === "monthly"
                          ? `${startDate.toLocaleDateString("pt-BR", {
                              month: "long",
                              year: "numeric",
                            })} - ${endDate.toLocaleDateString("pt-BR", {
                              month: "long",
                              year: "numeric",
                            })}`
                          : `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
                        : "Selecione um período"}
                    </span>
                  </p>
                  <br />
                </div>
                <div className="col-span-6">
                  <p className="main-card-icon mb-0">
                    <svg
                      className="svg-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      height="24px"
                      viewBox="0 0 24 24"
                      width="24px"
                      fill="#000000"
                    >
                      <path d="M0 0h24v24H0V0z" fill="none" />
                      <path
                        d="M13 4H6v16h12V9h-5V4zm3 14H8v-2h8v2zm0-6v2H8v-2h8z"
                        opacity=".3"
                      />
                      <path d="M8 16h8v2H8zm0-4h8v2H8zm6-10H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z" />
                    </svg>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Mensagens Recebidas */}
        <div className="xl:col-span-3 lg:col-span-6 md:col-span-6 sm:col-span-6 col-span-12">
          <div className="box">
            <div className="box-body" style={{ height: "140px" }}>
              <div className="grid grid-cols-12">
                <div className="col-span-6 pe-0">
                  <p className="mb-2">
                    <span className="text-[1rem]">Mensagens Recebidas</span>
                  </p>
                  <p className="mb-2 text-[0.75rem]">
                    <span className="text-[1.5625rem] font-semibold leading-none vertical-bottom mb-0">
                      {isLoading
                        ? "-"
                        : messagesReceivedMetrics.totalMessagesReceived.total}
                    </span>
                    <span className="block text-[0.625rem] font-semibold text-[#8c9097] dark:text-white/50">
                      {startDate && endDate
                        ? grouping === "monthly"
                          ? `${startDate.toLocaleDateString("pt-BR", {
                              month: "long",
                              year: "numeric",
                            })} - ${endDate.toLocaleDateString("pt-BR", {
                              month: "long",
                              year: "numeric",
                            })}`
                          : `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
                        : "Selecione um período"}
                    </span>
                  </p>
                  <br />
                </div>
                <div className="col-span-6">
                  <p className="main-card-icon mb-0">
                    <svg
                      className="svg-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      height="24px"
                      viewBox="0 0 24 24"
                      width="24px"
                      fill="#000000"
                    >
                      <path d="M0 0h24v24H0V0z" fill="none" />
                      <path
                        d="M13 4H6v16h12V9h-5V4zm3 14H8v-2h8v2zm0-6v2H8v-2h8z"
                        opacity=".3"
                      />
                      <path d="M8 16h8v2H8zm0-4h8v2H8zm6-10H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z" />
                    </svg>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Respostas Enviadas */}
        <div className="xl:col-span-3 lg:col-span-6 md:col-span-6 sm:col-span-6 col-span-12">
          <div className="box">
            <div className="box-body" style={{ height: "140px" }}>
              <div className="grid grid-cols-12">
                <div className="col-span-6 pe-0">
                  <p className="mb-2">
                    <span className="text-[1rem]">Respostas Enviadas</span>
                  </p>
                  <p className="mb-2 text-[0.75rem]">
                    <span className="text-[1.5625rem] font-semibold leading-none vertical-bottom mb-0">
                      {isLoading
                        ? "-"
                        : answerMessagesSentMetrics.totalAnswerMessagesSent
                            .total}
                    </span>
                    <span className="block text-[0.625rem] font-semibold text-[#8c9097] dark:text-white/50">
                      {startDate && endDate
                        ? grouping === "monthly"
                          ? `${startDate.toLocaleDateString("pt-BR", {
                              month: "long",
                              year: "numeric",
                            })} - ${endDate.toLocaleDateString("pt-BR", {
                              month: "long",
                              year: "numeric",
                            })}`
                          : `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
                        : "Selecione um período"}
                    </span>
                  </p>
                  <br />
                </div>
                <div className="col-span-6">
                  <p className="main-card-icon mb-0">
                    <svg
                      className="svg-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      height="24px"
                      viewBox="0 0 24 24"
                      width="24px"
                      fill="#000000"
                    >
                      <path d="M0 0h24v24H0V0z" fill="none" />
                      <path
                        d="M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm1.23 13.33V19H10.9v-1.69c-1.5-.31-2.77-1.28-2.86-2.97h1.71c.09.92.72 1.64 2.32 1.64 1.71 0 2.1-.86 2.1-1.39 0-.73-.39-1.41-2.34-1.87-2.17-.53-3.66-1.42-3.66-3.21 0-1.51 1.22-2.48 2.72-2.81V5h2.34v1.71c1.63.39 2.44 1.63 2.49 2.97h-1.71c-.04-.97-.56-1.64-1.94-1.64-1.31 0-2.1.59-2.1 1.43 0 .73.57 1.22 2.34 1.67 1.77.46 3.66 1.22 3.66 3.42-.01 1.6-1.21 2.48-2.74 2.77z"
                        opacity=".3"
                      />
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z" />
                    </svg>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Acordos 100% IA */}
        <div className="xl:col-span-3 lg:col-span-6 md:col-span-6 sm:col-span-6 col-span-12">
          <div className="box">
            <div className="box-body" style={{ height: "140px" }}>
              <div className="grid grid-cols-12">
                <div className="col-span-12 pe-0">
                  <p className="mb-2" style={{ width: "30%" }}>
                    <span className="text-[1rem]">Acordos 100% IA</span>
                  </p>
                  <p className="mb-2 text-[0.75rem]">
                    <span className="text-[1.5625rem] font-semibold leading-none vertical-bottom mb-0">
                      {isLoading ? "-" : onlyAIMetrics}
                    </span>
                    <span
                      className="block text-[0.625rem] font-semibold text-[#8c9097] dark:text-white/50"
                      style={{ width: "50%" }}
                    >
                      {startDate && endDate
                        ? grouping === "monthly"
                          ? `${startDate.toLocaleDateString("pt-BR", {
                              month: "long",
                              year: "numeric",
                            })} - ${endDate.toLocaleDateString("pt-BR", {
                              month: "long",
                              year: "numeric",
                            })}`
                          : `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
                        : "Selecione um período"}
                    </span>
                  </p>
                  <br />
                </div>
                <div className="col-span-6">
                  <p className="main-card-icon mb-0">
                    <svg
                      className="svg-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      height="24px"
                      viewBox="0 0 24 24"
                      width="24px"
                      fill="#000000"
                    >
                      <path d="M0 0h24v24H0V0z" fill="none" />
                      <path
                        d="M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm1.23 13.33V19H10.9v-1.69c-1.5-.31-2.77-1.28-2.86-2.97h1.71c.09.92.72 1.64 2.32 1.64 1.71 0 2.1-.86 2.1-1.39 0-.73-.39-1.41-2.34-1.87-2.17-.53-3.66-1.42-3.66-3.21 0-1.51 1.22-2.48 2.72-2.81V5h2.34v1.71c1.63.39 2.44 1.63 2.49 2.97h-1.71c-.04-.97-.56-1.64-1.94-1.64-1.31 0-2.1.59-2.1 1.43 0 .73.57 1.22 2.34 1.67 1.77.46 3.66 1.22 3.66 3.42-.01 1.6-1.21 2.48-2.74 2.77z"
                        opacity=".3"
                      />
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z" />
                    </svg>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardsMetrics;
