"use client";

import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { ApexOptions } from "apexcharts";
import { useDateContext } from "../_context/DateContext";
import {
  fetchMessagesReceivedMetrics,
  fetchFirstMessagesSentMetrics,
  fetchAnswerMessagesSentMetrics,
} from "@/shared/_services/portfolio.provider";

const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

export default function IARecoveryChart() {
  const { dateRange, grouping } = useDateContext();
  const [startDate, endDate] = dateRange;

  const [rawKeys, setRawKeys] = useState<string[]>([]);
  const [labels, setLabels] = useState<string[]>([]);
  const [attendances, setAttendances] = useState<number[]>([]);
  const [messagesReceived, setMessagesReceived] = useState<number[]>([]);
  const [answersSent, setAnswersSent] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const isoDate = (d: Date) =>
    `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}-${String(
      d.getDate()
    ).padStart(2, "0")}`;

  const getDays = (start: Date, end: Date) => {
    const days: string[] = [];
    const cur = new Date(
      start.getFullYear(),
      start.getMonth(),
      start.getDate()
    );
    const last = new Date(end.getFullYear(), end.getMonth(), end.getDate());
    while (cur <= last) {
      days.push(isoDate(cur));
      cur.setDate(cur.getDate() + 1);
    }
    return days;
  };

  const getMonths = (start: Date, end: Date) => {
    const months: string[] = [];
    const cur = new Date(start.getFullYear(), start.getMonth(), 1);
    const last = new Date(end.getFullYear(), end.getMonth(), 1);
    while (cur <= last) {
      months.push(isoDate(cur));
      cur.setMonth(cur.getMonth() + 1);
    }
    return months;
  };

  const sumByMonth = (totals: Record<string, number>, months: string[]) =>
    months.map((m) => {
      const prefix = m.slice(0, 7);
      return Object.entries(totals)
        .filter(([date]) => date.startsWith(prefix))
        .reduce((sum, [, v]) => sum + v, 0);
    });

  useEffect(() => {
    if (!startDate || !endDate) {
      setRawKeys([]);
      setLabels([]);
      setAttendances([]);
      setMessagesReceived([]);
      setAnswersSent([]);
      return;
    }
    setIsLoading(true);

    const adjustedEnd =
      grouping === "monthly"
        ? new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0)
        : endDate;

    const from = isoDate(startDate);
    const to = isoDate(adjustedEnd);

    Promise.all([
      fetchFirstMessagesSentMetrics(from, to),
      fetchMessagesReceivedMetrics(from, to),
      fetchAnswerMessagesSentMetrics(from, to),
    ])
      .then(([attRes, recRes, ansRes]) => {
        const attTotals =
          attRes?.data?.totalFirstMessagesSent?.dailyTotals ??
          attRes?.totalFirstMessagesSent?.dailyTotals ??
          {};
        const recTotals =
          recRes?.data?.totalMessagesReceived?.dailyTotals ??
          recRes?.totalMessagesReceived?.dailyTotals ??
          {};
        const ansTotals =
          ansRes?.data?.totalAnswerMessagesSent?.dailyTotals ??
          ansRes?.totalAnswerMessagesSent?.dailyTotals ??
          {};

        if (grouping === "daily") {
          const days = getDays(startDate, adjustedEnd);
          setRawKeys(days);
          setLabels(
            days.map((d) => {
              const [y, m, day] = d.split("-").map(Number);
              return `${String(day).padStart(2, "0")}/${String(m).padStart(2, "0")}`;
            })
          );
          setAttendances(days.map((d) => attTotals[d] || 0));
          setMessagesReceived(days.map((d) => recTotals[d] || 0));
          setAnswersSent(days.map((d) => ansTotals[d] || 0));
        } else {
          const months = getMonths(startDate, adjustedEnd);
          setRawKeys(months);
          setLabels(
            months.map((d) => {
              const [y, m] = d.split("-").map(Number);
              return new Date(y, m - 1, 1)
                .toLocaleDateString("pt-BR", { month: "long" })
                .replace(/^./, (c) => c.toUpperCase());
            })
          );
          setAttendances(sumByMonth(attTotals, months));
          setMessagesReceived(sumByMonth(recTotals, months));
          setAnswersSent(sumByMonth(ansTotals, months));
        }
      })
      .catch(console.error)
      .finally(() => {
        setIsLoading(false);
        setTimeout(() => window.dispatchEvent(new Event("resize")), 0);
      });
  }, [startDate, endDate, grouping]);

  const options: ApexOptions = {
    chart: {
      type: "line",
      height: 350,
      toolbar: { show: false },
      zoom: { enabled: false },
      animations: {
        enabled: false,
        dynamicAnimation: { enabled: false },
        animateGradually: { enabled: false },
      },
      redrawOnParentResize: false,
      redrawOnWindowResize: false,
    },
    stroke: { width: [3, 3, 3], curve: "smooth" },
    markers: { size: 5 },
    colors: ["#191982", "#17b0ae", "#FF9900"],
    dataLabels: { enabled: false },
    xaxis: {
      type: "category",
      categories: labels,
      tickPlacement: "on",
      title: { text: grouping === "monthly" ? "Mês" : "Dia" },
      labels: { rotate: -45, hideOverlappingLabels: false },
    },
    yaxis: [{ title: { text: "Quantidade" } }],
    tooltip: {
      shared: true,
      intersect: false,
      x: { show: false },
      y: {
        formatter: (val) => `${val}`,
      },
    },
    legend: { position: "top" },
  };

  const series = [
    { name: "Atendimentos Iniciados", data: attendances },
    { name: "Mensagens Recebidas", data: messagesReceived },
    { name: "Respostas Enviadas", data: answersSent },
  ];

  return (
    <div style={{ position: "relative" }}>
      {rawKeys.length > 0 && (
        <ReactApexChart
          options={options}
          series={series}
          type="line"
          key={labels.join("_")}
          height={350}
        />
      )}
      {(!startDate || !endDate) && (
        <Overlay message="Selecione um período para visualizar o gráfico" />
      )}
      {isLoading && <Overlay message="Carregando informações..." />}
    </div>
  );
}

function Overlay({ message }: { message: string }) {
  return (
    <div
      style={{
        position: "absolute",
        inset: 0,
        backgroundColor: "rgba(255,255,255,0.4)",
        backdropFilter: "blur(5px)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1,
        borderRadius: "8px",
        pointerEvents: "none",
      }}
    >
      <span
        style={{
          color: "#333",
          fontWeight: 500,
          fontSize: "1rem",
          background: "rgba(255,255,255,0.6)",
          padding: "0.5rem 1rem",
          borderRadius: "6px",
        }}
      >
        {message}
      </span>
    </div>
  );
}
