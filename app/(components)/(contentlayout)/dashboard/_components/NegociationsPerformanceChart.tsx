"use client";
import { ApexOptions } from "apexcharts";
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import {
  fetchPortfolioItemsWithInteractionMetrics,
  fetchPortfolioItemsGroupedByDateMetrics,
} from "@/shared/_services/portfolio.provider";
import { useDateContext } from "../_context/DateContext";

const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

interface spark3 {
  options: ApexOptions;
  width?: number;
  height?: string | number;
  series: ApexAxisChartSeries;
  label?: string;
  color?: string | string[] | (string & string[]) | undefined;
  endingShape?: string;
  enabled?: boolean;
}

export default function NegociationsPerformanceChart() {
  const { dateRange, grouping } = useDateContext();
  const [startDate, endDate] = dateRange;
  const [isLoading, setIsLoading] = useState(true);
  
  const [chartData, setChartData] = useState<spark3>({
    series: [
      { name: "Negociações com Interação", data: new Array(12).fill(0) },
      { name: "Acordos", data: new Array(12).fill(0) },
    ],
    options: {
      chart: { stacked: true, type: "bar", height: 325 },
      grid: { borderColor: "#f5f4f4", strokeDashArray: 5 },
      colors: ["rgb(17, 28, 138)", "rgb(60, 92, 181)"],
      plotOptions: { bar: { columnWidth: "20%" } },
      dataLabels: { enabled: false },
      legend: { show: true, position: "top" },
      yaxis: { title: { text: "Negociações", style: { fontSize: "14px" } } },
      xaxis: { categories: []
    },
    },
  });


  
  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      if (!startDate || !endDate) return;
  
      try {
        let adjustedEndDate = endDate;
  
        if (grouping === "monthly") {
          adjustedEndDate = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0);
        }
  
        const formattedStartDate = formatDateLocal(startDate);
        const formattedEndDate = formatDateLocal(adjustedEndDate);
        const groupByDate = grouping === "monthly" ? "MONTH" : "DAY";
  
        const interactionsData = await fetchPortfolioItemsWithInteractionMetrics(
          formattedStartDate,
          formattedEndDate,
          groupByDate
        );
  
        const agreementsData = await fetchPortfolioItemsGroupedByDateMetrics(
          formattedStartDate,
          formattedEndDate,
          groupByDate
        );
  
        const categories =
          grouping === "monthly"
            ? getMonthStartDatesBetween(startDate, adjustedEndDate)
            : getDaysBetweenDates(startDate, adjustedEndDate);
  
        const interactionSeries = categories.map(
          (date) => interactionsData?.[date] || 0
        );
  
        const agreementsSeries = categories.map(
          (date) => agreementsData?.[date] || 0
        );

        setChartData((prevData) => ({
          ...prevData,
          options: {
            ...prevData.options,
            xaxis: {
              ...prevData.options.xaxis,
              categories: categories,
              labels: {
                rotate: -90,
                formatter: (value: string) => {
                  const [year, month, day] = value.split("-").map(Number);
                  const date = new Date(year, month - 1, day);
                
                  if (isNaN(date.getTime())) return value;
                
                  if (grouping === "monthly") {
                    return date.toLocaleDateString("pt-BR", { month: "long" }).replace(/^./, (str) => str.toUpperCase());
                  } else {
                    return date.toLocaleDateString("pt-BR", { day: "2-digit", month: "2-digit" });
                  }
                }
                
                              
              }
            },
          },
          series: [
            { name: "Negociações com Interação", data: interactionSeries },
            { name: "Acordos", data: agreementsSeries },
          ],
        }));
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    }
  
    fetchData();
  }, [startDate, endDate, grouping]);
  
  
  

  function formatDateLocal(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`; 
  }
  
  
  
  function getDaysBetweenDates(start: Date, end: Date) {
    const dates = [];
    const currentDate = new Date(start.getFullYear(), start.getMonth(), start.getDate()); 
    const finalDate = new Date(end.getFullYear(), end.getMonth(), end.getDate()); 
    while (currentDate <= finalDate) {
      dates.push(formatDateLocal(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return dates;
  }
  
  function getMonthStartDatesBetween(start: Date, end: Date) {
    const dates = [];
    const currentDate = new Date(start.getFullYear(), start.getMonth(), 1); 
    const finalDate = new Date(end.getFullYear(), end.getMonth(), end.getDate()); 
    while (currentDate <= finalDate) {
      dates.push(formatDateLocal(currentDate));
      currentDate.setMonth(currentDate.getMonth() + 1);
    }
    return dates;
  }

  return (
    <div id="salesOverview" style={{ position: "relative" }}>
      <ReactApexChart
        options={chartData.options}
        series={chartData.series}
        type="bar"
        width="100%"
        height={325}
      />

      {(!startDate || !endDate) && (
        <Overlay message="Selecione um período para visualizar o gráfico" />
      )}

      {startDate && endDate && isLoading && (
        <Overlay message="Carregando informações..." />
      )}
    </div>
  );
}

function Overlay({ message }: { message: string }) {
  return (
    <div
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(255, 255, 255, 0.4)",
        backdropFilter: "blur(5px)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1,
        borderRadius: "8px",
        pointerEvents: "none",
      }}
    >
      <span
        style={{
          color: "#333",
          fontWeight: 500,
          fontSize: "1rem",
          background: "rgba(255,255,255,0.6)",
          padding: "0.5rem 1rem",
          borderRadius: "6px",
        }}
      >
        {message}
      </span>
    </div>
  );
}