import React, { createContext, useContext, useState, ReactNode } from "react";
interface DateContextType {
  dateRange: [Date | null, Date | null];
  setDateRange: (range: [Date | null, Date | null]) => void;
  grouping: "daily" | "monthly";
  setGrouping: (grouping: "daily" | "monthly") => void;
}

const DateContext = createContext<DateContextType | undefined>(undefined);

export function DateProvider({ children }: { children: ReactNode }) {
  const today = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(today.getDate() - 6);

  const savedFilters =
    typeof window !== "undefined"
      ? localStorage.getItem("cardsMetricsFilters")
      : null;
  let initialDateRange: [Date | null, Date | null] = [sevenDaysAgo, today];
  let initialGrouping: "daily" | "monthly" = "daily";

  if (savedFilters) {
    try {
      const {
        startDate: savedStart,
        endDate: savedEnd,
        grouping: savedGrouping,
      } = JSON.parse(savedFilters);
      if (savedStart && savedEnd) {
        initialDateRange = [new Date(savedStart), new Date(savedEnd)];
      }
      if (savedGrouping === "daily" || savedGrouping === "monthly") {
        initialGrouping = savedGrouping;
      }
    } catch (error) {
      console.error("Erro ao ler filtros salvos:", error);
    }
  }

  const [dateRange, setDateRange] =
    useState<[Date | null, Date | null]>(initialDateRange);
  const [grouping, setGrouping] = useState<"daily" | "monthly">(
    initialGrouping
  );

  const value = {
    dateRange,
    setDateRange,
    grouping,
    setGrouping,
  };

  return <DateContext.Provider value={value}>{children}</DateContext.Provider>;
}

export function useDateContext() {
  const context = useContext(DateContext);
  if (context === undefined) {
    throw new Error("useDateContext must be used within a DateProvider");
  }
  return context;
}
