"use client";
import React from "react";
import NegociationsPerformanceChart from "./_components/NegociationsPerformanceChart";
import NegociationsTable from "./_components/NegociationsTable";
import CardsMetrics from "./_components/CardsMetrics";
import { DateProvider } from "./_context/DateContext";
import IARecoveryChart from "./_components/IARecoveryChart";
const Dashboard = () => {
  return (
    <DateProvider>
      <div>
        <div className="flex flex-col gap-6">
          <CardsMetrics />
          <div className="box width-print">
            <div className="box-header avoid-break">
              <div className="box-title">
                Evolução de Resultados da Cobrança Automatizada
              </div>
            </div>
            <div className="box-body pb-5 print-body">
              <IARecoveryChart />
            </div>
          </div>
          <div className="box width-print">
            <div className="page-break"></div>
            <div className="box-header avoid-break">
              <div className="box-title">Performance das Negociações</div>
            </div>
            <div className="box-body pb-5 print-body">
              <NegociationsPerformanceChart />
            </div>
          </div>
        </div>
        <div className="page-break"></div>
        <div className="flex flex-col gap-6 avoid-break">
          <NegociationsTable
            title="Negociações Esperando por Você"
            waitingBusinessUserResponseFilter="true"
          />
          <div className="page-break"></div>
        </div>
      </div>
    </DateProvider>
  );
};

export default Dashboard;
