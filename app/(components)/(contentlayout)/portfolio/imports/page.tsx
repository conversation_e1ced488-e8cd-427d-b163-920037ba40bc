import Link from 'next/link';
import { Metadata } from 'next';
import { twMerge } from 'tailwind-merge';
import {
  IconNewPortfolio,
  IconPortfolioFile,
  // IconRefresh,
} from '@/shared/layout-components/icons';
import PageHeading from '@/shared/layout-components/page-heading/PageHeading';
import { PortfolioImportsList } from './_components/PortfolioImportsList';

export default function PortfolioImportPage() {
  return (
    <div className={twMerge('PortfolioImportPage')}>
      <PageHeading
        icon={<IconPortfolioFile />}
        primary='Importações'
        secondary={
          <>
            <div className='flex gap-2 items-start'>
              {/* <button className='ti-btn bg-gray-700 text-white ti-btn-wave'>
                Atualizar <IconRefresh className='text-lg' />
              </button> */}
              <Link
                href='/portfolio/imports/create'
                className='ti-btn ti-btn-success-full ti-btn-wave'
                style={{ textDecoration: 'none', backgroundColor: '#111c8a' }}
              >
                <IconNewPortfolio className='me-1 text-lg' />
                Importar Portfólio
              </Link>
            </div>
          </>
        }
      />

      {/* <div className="px-4">search / filters</div> */}

      <div className='px-1 md:px-4 pt-4'>
        <div className='box'>
          <div className='box-body !p-0'>
            <PortfolioImportsList />
          </div>
        </div>
      </div>

      {/*  */}
    </div>
  );
}

export const metadata: Metadata = {
  title: 'Importações',
  description: 'Adicione um novo portfólio.',
};
