"use client";
import { useEffect, useState } from "react";
import Link from "next/link";
import { twMerge } from "tailwind-merge";
import {
  IconPortfolioImportStatusEnum,
  IconDownload,
  IconPortfolioPerformance,
  IconListing,
} from "@/shared/layout-components/icons";

import {
  downloadImportErrorsFile,
  downloadOriginalFile,
  exportPortfolioData,
  getAllPortfolios,
  Portfolio,
  PortfolioImportStatus,
} from "@/shared/_services/portfolio.provider";
import { formatDateAgo, formatDateAgoFactory, formatDateSimple, formatRelativeDate } from "@/shared/_utils/formatters/formatDate";
import { formatNumber } from "@/shared/_utils/formatters/formatNumber";
import { DropdownButton } from "@/shared/components/DropdownButton";

const fmtDateAgo = formatDateAgoFactory({ style: "narrow", maxParts: 3 });

export function PortfolioImportsList() {
  const [downloading, setDownloading] = useState<string>("");
  const [portfolioList, setPortfolioList] = useState<Portfolio[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const handleDownload = async (portfolioId: string) => {
    setDownloading(portfolioId);

    try {
      const portfolio = portfolioList.filter((p) => p.id === portfolioId)[0];
      const blob = await downloadOriginalFile(portfolioId);
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = portfolio.originalFileName;
      document.body.appendChild(a);
      a.click();
      a.remove();
      setTimeout(() => {
        setDownloading("");
      }, 2000);
    } catch (error: any) {
      console.log("error", error);
    }
  };

  const handleImportErrorDownload = async (portfolioId: string) => {
    setDownloading(portfolioId);

    try {
      const portfolio = portfolioList.filter((p) => p.id === portfolioId)[0];
      const blob = await downloadImportErrorsFile(portfolioId);
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = portfolio.name + "-import-errors.csv";
      document.body.appendChild(a);
      a.click();
      a.remove();
      setTimeout(() => {
        setDownloading("");
      }, 2000);
    } catch (error: any) {
      console.log("error", error);
    }
  };

  const handleExportData = async (portfolioId: string) => {
    setDownloading(portfolioId);

    try {
      const portfolio = portfolioList.filter((p) => p.id === portfolioId)[0];
      const blob = await exportPortfolioData(portfolioId);
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = portfolio.name + "-export.csv";
      document.body.appendChild(a);
      a.click();
      a.remove();
      setTimeout(() => {
        setDownloading("");
      }, 2000);
    } catch (error: any) {
      console.log("error", error);
    }
  };

  useEffect(() => {
    getAllPortfolios()
      .then((portfolios) => {
        setPortfolioList(portfolios);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  return (
    <ul className={"PortfolioImportsList"}>
      {isLoading ? (
        <li key="loading" className="p-4">
          Carregando Portfólios...
        </li>
      ) : (
        <>
          {!portfolioList || portfolioList.length === 0 ? (
            <li key="empty" className="p-4">
              Nenhum dado: ainda não há nenhum portfólio em processamento.
            </li>
          ) : (
            <>
              {portfolioList.map((portfolio, portfolioIdx) => {
                const total = portfolio.totalQuantity || 0;
                const currProcessed = portfolio.processedQuantity || 0;
                const percentage = Math.round((currProcessed / total) * 100);
                console.log(portfolio.createdAt);

                let qtdProcess = 0;

                if (total !== 0 && currProcessed !== 0) {
                  qtdProcess =
                    Math.floor((currProcessed / total) * 10 ** 5) / 10 ** 3;
                }

                const currFailed = portfolio.totalFailedQuantity || 0;

                const currSuccess = portfolio.totalSuccessQuantity || 0;

                const isNotDone = [
                  PortfolioImportStatus.PROCESSING,
                  PortfolioImportStatus.UPLOADED,
                ].includes(portfolio.importStatus as PortfolioImportStatus);
                const isProcessing =
                  isNotDone && Math.floor(total - currProcessed) > 0;
                const isUploaded =
                  [PortfolioImportStatus.UPLOADED].includes(
                    portfolio.importStatus as PortfolioImportStatus
                  ) && qtdProcess === 0;
                const isSuccess =
                  portfolio.importStatus === PortfolioImportStatus.SUCCESS;

                let IconStatusImport =
                  IconPortfolioImportStatusEnum[
                    PortfolioImportStatus.PROCESSING
                  ];
                if (isUploaded)
                  IconStatusImport =
                    IconPortfolioImportStatusEnum[
                      PortfolioImportStatus.UPLOADED
                    ];
                if (isProcessing)
                  IconStatusImport =
                    IconPortfolioImportStatusEnum[
                      PortfolioImportStatus.PROCESSING
                    ];
                if (isSuccess)
                  IconStatusImport =
                    IconPortfolioImportStatusEnum[
                      PortfolioImportStatus.SUCCESS
                    ];

                return (
                  <div
                    key={portfolio.id}
                    className={twMerge(
                      "px-4 py-4 flex items-center",
                      portfolioIdx > 0 && "border-t",
                      "dark:border-defaultborder/30 border-slate-200",
                      "hover:bg-success/10",
                      "group"
                    )}
                  >
                    <div
                      className={twMerge(
                        // "svg-icon-background",
                        // isProcessing && "bg-warning/10",
                        isProcessing && "!stroke-warning",
                        // isUploaded && "bg-primary/10",
                        isUploaded && "!stroke-primary",
                        // isSuccess && "bg-success/10",
                        isSuccess && "!stroke-success",
                        "me-2 md:me-6"
                      )}
                    >
                      <IconStatusImport
                        style={{
                          stroke: "inherit",
                          width: "1em ",
                          height: "1em",
                          fontSize: "2rem",
                        }}
                        width="2em"
                        height="2em"
                      />
                    </div>
                    <div className="flex-grow flex flex-row flex-wrap">
                      {/* w-full */}
                      <div className="flex flex-col w-6/12 gap-2 justify-between items-start  ">
                        <div className="flex flex-col w-full">
                          {/* title and date */}
                          <h6 className="flex align-middle flex-wrap gap-2 text-[1.125rem] font-semibold pb-1 whitespace-nowrap overflow-hidden text-ellipsis">
                            {portfolio.name}
                          </h6>
                          <div className="!text-[0.75rem] !font-medium  md:block text-muted">
                            <p>
                              Adicionado há {formatRelativeDate(portfolio.createdAt.toString())}
                            </p>
                            <p>
                              {isUploaded ? (
                                <span className="count-up text-primary">
                                  Em fila
                                </span>
                              ) : (
                                <>
                                  <span className="count-up text-danger">
                                    {formatNumber(currFailed, 0)}{" "}
                                    <span className="hidden md:inline">
                                      falharam
                                    </span>
                                  </span>
                                  <span className="text-muted">
                                    &nbsp; | &nbsp;
                                  </span>
                                  <span className="count-up text-success">
                                    {formatNumber(currSuccess, 0)}{" "}
                                    <span className="hidden md:inline">
                                      adicionados
                                    </span>
                                  </span> 
                                  <span className="text-muted">
                                    &nbsp; | &nbsp;
                                  </span>
                                  <span className="count-up" style={{ color: "rgb(17, 28, 138)" }}>
                                       {formatNumber(total, 0)}{" "}
                                       <span className="hidden md:inline">
                                     processados
                                    </span>
                                    </span>
                                </>
                              )}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col w-6/12 justify-between gap-4 items-start md:items-center md:ml-auto ">
                        {/* action buttons */}
                        <div className="flex flex-wrap gap-4 w-full justify-end">
                          {isSuccess && (
                            <>
                              <Link
                                href={`/portfolio/performance/${portfolio.id}`}
                                className="inline-flex align-center justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                              >
                                Performance
                                <IconPortfolioPerformance className="-mr-1 size-5 text-gray-400" />
                              </Link>
                              <Link
                                href={`/negotiation?portfolio=${portfolio.id}`}
                                className="inline-flex align-center justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                              >
                                Negociações
                                <IconListing className="-mr-1 size-5 text-gray-400" />
                              </Link>
                            </>
                          )}
                          <DropdownButton
                            label={
                              downloading === portfolio.id
                                ? "Downloading"
                                : "Download"
                            }
                            icon={
                              downloading === portfolio.id ? (
                                <svg
                                  aria-hidden="true"
                                  className="w-10 h-10 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600 max-h-20 max-w-24"
                                  viewBox="0 0 100 101"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="currentColor"
                                  />
                                  <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentFill"
                                  />
                                </svg>
                              ) : (
                                <IconDownload
                                  aria-hidden="true"
                                  className="size-4 text-gray-400"
                                />
                              )
                            }
                            options={[
                              {
                                label: "Arquivo original",
                                key: `uploadedFile-${portfolio.id}`,
                                onClick: () => handleDownload(portfolio.id),
                              },
                              {
                                label: "Exportar dados",
                                key: `exportData-${portfolio.id}`,
                                onClick: () => handleExportData(portfolio.id),
                              },
                              ...(currFailed > 0 && isSuccess
                                ? [
                                    {
                                      label: "Itens com falha",
                                      key: `failedFile-${portfolio.id}`,
                                      onClick: () =>
                                        handleImportErrorDownload(portfolio.id),
                                    },
                                  ]
                                : []),
                            ]}
                          />
                        </div>
                        {/* progress bar */}
                        <div className="w-full">
                          <div
                            className={twMerge(
                              "flex w-full h-[0.7rem]",
                              "rounded-full overflow-hidden ",
                              "border border-black/20 dark:border-white/20 bg-light"
                            )}
                          >
                            <div
                              className={twMerge(
                                "bg-bluemain",
                                "flex flex-col justify-center rounded-none overflow-hidden "
                              )}
                              style={{ width: `${percentage}%` }}
                              aria-valuenow={percentage}
                              aria-valuemin={0}
                              aria-valuemax={100}
                            />
                          </div>

                          <div className="flex items-center w-full text-[.725rem] font-normal mt-1">
                            {percentage}%{" "}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </>
          )}
        </>
      )}
    </ul>
  );
}
