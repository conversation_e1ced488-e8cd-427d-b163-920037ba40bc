'use client';
import { PropsWithChildren, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { IconClose, IconDelete } from '@/shared/layout-components/icons';

export interface IBtnDeletePortfolioProps
  extends Partial<PropsWithChildren<JSX.IntrinsicElements['button']>> {
  portfolioId?: string;
  iconOnly?: boolean;
}

export default function BtnDeletePortfolio({
  className,
  portfolioId = '',
  disabled,
  iconOnly,
  ...restProps
}: IBtnDeletePortfolioProps) {
  const [showConfirmationModal, setShowConfirmationModal] =
    useState<boolean>(false);

  const handleDeletePortfolio = () => {
    console.log('delete portfolio');
    setShowConfirmationModal(false);
  };
  const handleOpenModal = () => setShowConfirmationModal(true);

  return (
    <>
      <button
        {...restProps}
        disabled={!portfolioId || disabled}
        className={twMerge(
          'BtnDeletePortfolio',
          'hs-dropdown-toggle',
          'ti-btn ti-btn-danger ti-btn-wave px-2 py-5',
          iconOnly && 'bg-transparent',
          className,
        )}
        data-hs-overlay='#hs-delete-portfolio-modal'
        onClick={handleOpenModal}
      >
        <IconDelete className='me-1 text-lg' />
        {!iconOnly && 'Excluir Portfólio'}
      </button>

      {showConfirmationModal && (
        <div id='hs-delete-portfolio-modal' className='hs-overlay ti-modal'>
          <div className='hs-overlay-open:mt-7 ti-modal-box mt-0 ease-out max-w-64 min-h-[calc(100%-3.5rem)] flex items-center'>
            <div className='ti-modal-content min-h-60 '>
              <div className='ti-modal-header'>
                <h6 className='modal-title text-lg font-semibold'>
                  Excluir Portfólio
                </h6>
                <button
                  type='button'
                  className='hs-dropdown-toggle ti-modal-close-btn'
                  data-hs-overlay='#hs-delete-portfolio-modal'
                >
                  <span className='sr-only'>Fechar</span>
                  <IconClose className='text-2xl' />
                </button>
              </div>
              <div className='ti-modal-body flex-grow pb-4 text-base'>
                <p>
                  Deseja excluir este portfólio? Esta ação não poderá ser
                  desfeita.
                </p>
              </div>
              <div className='ti-modal-footer'>
                <button
                  type='button'
                  className='!px-4 ti-btn ti-btn-outline-dark ti-btn-wave'
                  data-hs-overlay='#hs-delete-portfolio-modal'
                >
                  Manter
                </button>
                <button
                  className='!px-4 ti-btn ti-btn-danger-full ti-btn-wave'
                  onClick={handleDeletePortfolio}
                >
                  Sim, excluir
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
