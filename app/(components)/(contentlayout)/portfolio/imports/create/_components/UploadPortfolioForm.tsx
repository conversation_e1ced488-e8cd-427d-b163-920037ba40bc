"use client";

import { getCustomer } from "@/shared/_services/customer.provider";
import FileCSVPreview, { FileInfo } from "./file-csv-preview/FileCsvPreview";
import {
  createPortfolio,
  IPortfolioUpload,
} from "@/shared/_services/portfolio.provider";
import {
  getAllWorkflows,
  ICustomerWorkflow,
} from "@/shared/_services/workflow.provider";
import getEnv from "@/shared/_utils/hooks/getEnv";
import TcdCheckbox from "@/shared/form-components/tcd-checkbox/TcdCheckbox";
import TcdInput from "@/shared/form-components/tcd-input/TcdInput";
import TcdSelectEntity from "@/shared/form-components/tcd-select/TcdSelectEntity";
import { IconSuccess, IconUpload } from "@/shared/layout-components/icons";

import Link from "next/link";
import React, { ChangeEventHandler, useState } from "react";
import { FaSpinner } from "react-icons/fa";
import { twMerge } from "tailwind-merge";

export default function UploadPortfolioForm() {
  const [portfolioName, setPortfolioName] = useState("");
  const [isPortfolioNameDirty, setIsPortfolioNameDirty] = useState(false);
  const [submitError, setSubmitError] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [communicationChannel, setCommunicationChaneel] = useState("WHATSAPPSELFHOSTED");

  const [editedFile, setEditedFile] = useState<File | null>(null);
  const [headPreview, setHeadPreview] = useState<string[]>([]);
  const [rowsPreview, setRowsPreview] = useState<(string | null)[][]>([]);

  const handleFileUpdate = (
    updatedFile: File,
    head: string[],
    rows: (string | null)[][]
  ) => {
    setEditedFile(updatedFile);
    setHeadPreview(head);
    setRowsPreview(rows);
  };

  // Load costumerId from local storage
  const customer = getCustomer();
  const customerId = customer.id;

  const handleFileChanged = (evt: FileInfo) => {
    if (!isPortfolioNameDirty) {
      setPortfolioName(
        evt.name
          .replace(/\.csv/gi, "")
          .replace(/[_\-./\\]/gi, " ")
          .replace(/[^a-z0-9 ]/gi, "")
          .toLowerCase()
          .split(" ")
          .map((word) => word.slice(0, 1).toUpperCase() + word.slice(1))
          .join(" ")
      );
    }
  };

  const handleFormSubmit: React.FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();

    const formData: any = Object.fromEntries(
      new FormData(event.target as HTMLFormElement).entries()
    );

    if (!editedFile && rowsPreview.length === 0) {
      setSubmitError("Select or edit a file before saving.");
      return;
    }

    const csvContent = [headPreview.join(",")]
      .concat(rowsPreview.map((row) => row.join(",")))
      .join("\n");

    const updatedFile = new File([csvContent], portfolioName + ".csv", {
      type: "text/csv",
    });

    const data: Partial<IPortfolioUpload> = {
      file: updatedFile,
      name: formData.name,
      workflowId: formData.workflowId,
      executeImmediately: Boolean(formData.executeImmediately),
      communicationChannel: communicationChannel,
    };

    if (!data.file?.size || data.file.size === 0) {
      setSubmitError("Select a file.");
      return;
    }
    if (!data.name) {
      setSubmitError("Give your portfolio a name");
      return;
    }
    if (!data.workflowId) {
      setSubmitError("Select the workflow.");
      return;
    }

    setIsUploading(true);

    createPortfolio(data as IPortfolioUpload)
      .then(() => {
        setSubmitSuccess(true);
      })
      .catch((err) => {
        console.log({ err });
        setSubmitError(String(err));
      })
      .finally(() => {
        setIsUploading(false);
      });
  };

  const portfolioNameChanged: ChangeEventHandler<HTMLInputElement> = (
    event
  ) => {
    setIsPortfolioNameDirty(Boolean(event.target.value));
    setPortfolioName(event.target.value);
  };

  const handleCommunicationCanalChange = (event: any) => {
    setCommunicationChaneel(event.target.value);
  };

  return submitSuccess ? (
    <div className="p-4 max-w-screen-lg">
      <div className="box">
        <div className="box-body flex flex-col gap-4 items-center">
          <IconSuccess className="text-success text-4xl" />
          <div className="flex items-center gap-4">
            <div>
              <p>
                Seu portfólio foi enviado com sucesso e está sendo processado.
                Você pode acompanhar o progresso na página de importações.
              </p>
            </div>
          </div>
        </div>
        <div className="box-footer">
          <div className="flex gap-4 items-start justify-end">
            <Link
              href="/portfolio/imports"
              className="ti-btn ti-btn-success-full ti-btn-wave"
              style={{ textDecoration: "none" }}
            >
              Ver importações
            </Link>
          </div>
        </div>
      </div>
    </div>
  ) : (
    <form className="p-4 max-w-screen-lg" onSubmit={handleFormSubmit}>
      <div className="box">
        <div className="box-body flex flex-col gap-4 ">
          {/*  */}
          <div className="flex gap-4 items-end justify-between">
            <TcdInput
              type="text"
              id="portfolioName"
              name="name"
              label="Nome"
              placeholder="Nome do Portfólio"
              value={portfolioName}
              onChange={portfolioNameChanged}
              disabled={isUploading}
              className="flex-grow max-w-md"
            />
          </div>

          <div className="flex flex-wrap lg:flex-nowrap gap-4 items-end justify-between">
            <TcdSelectEntity
              name="workflowId"
              placeholder="Selecione"
              className="max-w-md"
              defaultValue="43e3ceed-58da-4ff2-bddf-67cb79d4433f"
              disabled={isUploading}
              queryKey={["listWorkflows", "selectEntity"]}
              queryFn={() => getAllWorkflows(customerId)}
              select={(res: ICustomerWorkflow[]) =>
                (res || []).map((workflow) => ({
                  value: workflow.workflowId,
                  label: workflow.workflowName,
                }))
              }
            />
            <TcdCheckbox
              name="executeImmediately"
              label="Ativar outbound"
              disabled={isUploading}
              className="py-2 flex-grow-0"
            />
          </div>
          <div className="flex flex-wrap lg:flex-nowrap gap-4 mt-4">
            <label className="form-check-label" htmlFor="flexRadioDefault1">
              Canal de Comunicação:
            </label>
            <div className="form-check form-check-md flex items-center">
              <input
                className="form-check-input mr-1"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault1"
                value="WHATSAPPSELFHOSTED"
                defaultChecked
                onChange={handleCommunicationCanalChange}
              />
              <label className="form-check-label" htmlFor="flexRadioDefault1">
                WhatsApp
              </label>
              <input
                className="form-check-input ml-2 mr-1"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault2"
                value="SMS_VONAGE"
                onChange={handleCommunicationCanalChange}
              />
              <label className="form-check-label" htmlFor="flexRadioDefault2">
                SMS
              </label>
              <input
                className="form-check-input ml-2 mr-1"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault3"
                value="BLIP_COLINA"
                onChange={handleCommunicationCanalChange}
              />
              <label className="form-check-label" htmlFor="flexRadioDefault3">
                Blip
              </label>
            </div>
          </div>
          <div className="flex">
            <FileCSVPreview
              id="newPortfolioFile"
              className={twMerge("w-full", isUploading && "hidden")}
              name="file"
              onChange={handleFileChanged}
              onFileUpdate={handleFileUpdate}
            />
            <div
              style={{ backgroundColor: "rgb(var(--body-bg))" }}
              className={twMerge(
                "UploadProgress",
                "shadow-inner",
                "rounded-lg flex flex-col items-center justify-end gap-2 p-4 w-full",
                !isUploading && "hidden"
              )}
            >
              <div
                className={twMerge(
                  "flex items-center justify-center gap-4 w-full font-mono text-lg"
                )}
              >
                <FaSpinner className="text-lg animate-spin" />
                Aguarde realizando o upload do arquivo...
              </div>
            </div>
          </div>

          {/*  */}
        </div>
        <div className="box-footer">
          <div className="flex gap-4 items-start justify-end">
            <button
              type="submit"
              className={twMerge(
                "ti-btn ti-btn-wave",
                isUploading ? "ti-btn-dark opacity-70" : "ti-btn-success-full"
              )}
              disabled={isUploading}
              style={{ textDecoration: "none" }}
            >
              <IconUpload className="text-lg" />
              Salvar
            </button>
          </div>

          {submitError && (
            <div className="py-4 px-5 mt-2 rounded-md bg-pink-50 border-red-50 text-red-800 w-full">
              {submitError}
            </div>
          )}
        </div>
      </div>
    </form>
  );
}
