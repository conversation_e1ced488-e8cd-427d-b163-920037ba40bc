"use client";

import formatFileSize from "@/shared/_utils/formatters/formatFileSize";
import isNumberProbablyDate from "@/shared/_utils/isNumberProbablyDate";
import readFileChunk from "@/shared/_utils/readFileChunk";
import { IconSelectedFileCSV } from "@/shared/layout-components/icons";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  DragEventHandler,
  PropsWithChildren,
  useEffect,
  useRef,
  useState,
} from "react";
import { LuFolderSync } from "react-icons/lu";

import { twMerge } from "tailwind-merge";

export interface IFileCSVPreviewProps
  extends Omit<
    Partial<PropsWithChildren<JSX.IntrinsicElements["input"]>>,
    "id" | "onChange"
  > {
  onChange?: (fileInfo: FileInfo) => void;
  id: string;
  onFileUpdate?: (
    updatedFile: File,
    head: string[],
    rows: (string | null)[][]
  ) => void;
}

export interface FileInfo {
  file?: File | null;
  hasFile: boolean;
  name: string;
  size: number;
  type: string;
}

export default function FileCSVPreview(props: IFileCSVPreviewProps) {
  const {
    className,
    accept = ".csv",
    multiple = false,
    id,
    onChange,
    onFileUpdate,
    ...restProps
  } = props;
  const inputRef = useRef<HTMLInputElement>(null);
  const [fileInfo, setFileInfo] = useState<FileInfo>({
    hasFile: false,
    name: "",
    size: 0,
    type: "",
  });
  const [fileCursor, setFileCursor] = useState(0);
  const readFileChunkSize = 16 * 10 ** 4;
  const [currChunkSize, setCurrChunkSize] = useState(0);
  const [errorPreview, setErrorPreview] = useState("");
  const [rowsPreview, setRowsPreview] = useState<(string | null)[][]>([]);
  const [headPreview, setHeadPreview] = useState<string[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const [editingColumn, setEditingColumn] = useState<number | null>(null);
  const [tempColumnName, setTempColumnName] = useState<string>("");

  const handleColumnEditStart = (colIdx: number, currentName: string) => {
    setEditingColumn(colIdx);
    setTempColumnName(currentName);
  };

  const handleColumnEditChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempColumnName(e.target.value);
  };

  const handleColumnEditBlur = (colIdx: number) => {
    setHeadPreview((prev) =>
      prev.map((col, index) => (index === colIdx ? tempColumnName : col))
    );
    setEditingColumn(null);
  };

  const handleColumnEditKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    colIdx: number
  ) => {
    if (e.key === "Enter") {
      handleColumnEditBlur(colIdx);
    }
  };

  useEffect(() => {
    if (!fileInfo.file) return;

    const csvContent = [headPreview.join(",")]
      .concat(rowsPreview.map((row) => row.join(",")))
      .join("\n");

    const updatedFile = new File([csvContent], fileInfo.name, {
      type: "text/csv",
    });

    onFileUpdate?.(updatedFile, headPreview, rowsPreview);
  }, [headPreview, rowsPreview]);

  const addRow = (event?: React.MouseEvent<HTMLButtonElement>) => {
    event?.preventDefault();
    event?.stopPropagation();

    setRowsPreview((prevRows) => [
      ...prevRows,
      new Array(headPreview.length).fill(""),
    ]);
  };

  const removeRow = (
    rowIndex: number,
    event?: React.MouseEvent<HTMLButtonElement>
  ) => {
    event?.preventDefault();
    event?.stopPropagation();

    setRowsPreview(rowsPreview.filter((_, index) => index !== rowIndex));
  };

  const parseObjFileInfo = (objFile?: File | null | undefined) => {
    const parsedFileInfo = {
      file: objFile,
      hasFile: Boolean(objFile),
      name: objFile?.name || "",
      size: objFile?.size || 0,
      type: objFile?.type || "",
    };

    setFileInfo(parsedFileInfo);
    if (typeof onChange === "function") {
      onChange(parsedFileInfo);
    }
  };

  const handleFileChanged: ChangeEventHandler<HTMLInputElement> = (event) => {
    const objFile = event.target.files?.[0];
    if (!objFile) return;

    setFileInfo({
      file: objFile,
      hasFile: true,
      name: objFile.name,
      size: objFile.size,
      type: objFile.type,
    });

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;

      const rawLines = text.split(/\r?\n(?=(?:[^"]*"[^"]*")*[^"]*$)/);

      const data = rawLines.map((line) =>
        line
          .split(/,(?=(?:[^"]*"[^"]*")*[^"]*$)/)
          .map((cell) => cell.replace(/^"(.*)"$/s, "$1").replace(/""/g, '"'))
      );

      const header = data.shift() || [];
      setHeadPreview(header);
      setRowsPreview(data);
      onFileUpdate?.(objFile, header, data);
    };

    reader.readAsText(objFile);
  };

  const handleCellEdit = (rowIdx: number, cellIdx: number, value: string) => {
    setRowsPreview((prevRows) => {
      const updatedRows = [...prevRows];
      updatedRows[rowIdx][cellIdx] = value;
      return updatedRows;
    });
  };

  const handleDrop: DragEventHandler<HTMLDivElement> = (event) => {
    event.preventDefault();

    const droppedFiles = Array.from(event.dataTransfer.files);
    if (droppedFiles.length > 0) {
      parseObjFileInfo(droppedFiles[0]);
    }
  };

  const onDropAttrs = {
    onDragStart: (event: React.DragEvent<HTMLDivElement>) => {
      setIsDragging(true);
      // console.log("onDrag Start")
    },
    onDragEnter: (event: React.DragEvent<HTMLDivElement>) => {
      setIsDragging(true);
      // console.log("onDrag Enter")
    },
    onDragOver: (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
    },
    // onDrag: (event: React.DragEvent<HTMLDivElement>) => { event.preventDefault() }

    onDragLeave: (event: React.DragEvent<HTMLDivElement>) => {
      setIsDragging(false);
      // console.log("onDrag Leave")
    },
    onDragEnd: (event: React.DragEvent<HTMLDivElement>) => {
      setIsDragging(false);
      // console.log("onDrag End")
    },
    onDragExit: (event: React.DragEvent<HTMLDivElement>) => {
      setIsDragging(false);
      // console.log("onDrag Exit")
    },
    onDrop: (event: React.DragEvent<HTMLDivElement>) => {
      setIsDragging(false);
      handleDrop(event);
    },
  };

  useEffect(() => {
    if (!fileInfo.file) return;

    setErrorPreview("");
    try {
      readFileChunk(fileInfo.file, fileCursor, readFileChunkSize).then(
        (fileText) => {
          const rawLines = fileText.split(/\r?\n(?=(?:[^"]*"[^"]*")*[^"]*$)/);

          let numCols = headPreview.length;
          let fileHeaderLine = "";

          if (fileCursor === 0) {
            fileHeaderLine = rawLines.shift() || "";

            const header = fileHeaderLine
              .split(/,(?=(?:[^"]*"[^"]*")*[^"]*$)/)
              .map((cell) =>
                cell.replace(/^"(.*)"$/s, "$1").replace(/""/g, '"')
              );

            setHeadPreview(header);
            numCols = header.length;
          }

          rawLines.pop();

          const data = rawLines.map((line) => {
            const cells = line
              .split(/,(?=(?:[^"]*"[^"]*")*[^"]*$)/)
              .map((cell) =>
                cell
                  .replace(/^"(.*)"$/s, "$1")
                  .replace(/""/g, '"')
                  .replace(/\r?\n/g, " ")
                  .trim()
              );

            const rowData: (string | null)[] = new Array(numCols).fill(null);
            cells.forEach((value, idx) => {
              if (value !== "") rowData[idx] = value;
            });
            return rowData;
          });

          setRowsPreview(data);

          const rowsTextLength =
            rawLines.join("\n").length + fileHeaderLine.length;
          setCurrChunkSize(rowsTextLength);
        }
      );
    } catch (err: any) {
      setErrorPreview(`Error on rendering CSV preview: ${err.message || err}`);
    }
  }, [fileCursor, fileInfo.file]);

  return (
    <div
      className={twMerge(
        "FileCSVPreview",
        // "border ",
        // fileInfo.hasFile ? "border-1 bg-white" :
        "flex items-center justify-center",
        isDragging &&
          !fileInfo.hasFile &&
          "ring-8 ring-secondary-500 outline-4 outline-secondary-500",
        className
      )}
    >
      <input
        className="hidden"
        type="file"
        ref={inputRef}
        onChange={handleFileChanged}
        accept={accept}
        multiple={multiple}
        id={id}
        {...restProps}
      />
      {fileInfo.hasFile ? (
        <div
          className={twMerge(
            "w-full h-full",
            "rounded-lg",
            "flex flex-col items-center justify-center",
            "border border-zinc-300 bg-white"
          )}
        >
          <div
            className={twMerge(
              "flex flex-row flex-1 gap-2 items-center",
              "overflow-hidden rounded-t-lg",
              "w-full min-h-8",
              "pl-2",
              "bg-slate-200",
              "border-b border-zinc-400"
            )}
          >
            <IconSelectedFileCSV className="text-xl text-secondary-700" />
            <p className="text-sm font-medium">{fileInfo.name}</p>
            <p className="text-sm font-medium text-gray-500">
              {formatFileSize(fileInfo.size)}
            </p>
            <label
              htmlFor={id}
              tabIndex={0}
              className={twMerge(
                "flex items-center justify-center group ml-auto",
                "p-2",
                "rounded-0  text-orange-600",
                "cursor-pointer",
                "hover:bg-orange-200 hover:text-orange-800",
                "focus:outline-orange-600"
              )}
            >
              <LuFolderSync className="text-xl" />
            </label>
          </div>

          <div className="flex-grow h-80 overflow-auto resize-y max-w-full w-full">
            {errorPreview ? (
              <div className="flex items-center justify-center">
                {errorPreview}
              </div>
            ) : (
              <table className="table-auto text-left min-w-full">
                <thead>
                  <tr>
                    <th className="px-4 py-3 border-b border-r border-zinc-300 bg-stone-200"></th>
                    {headPreview.map((headCell, colIdx) => (
                      <th
                        key={`${headCell}${colIdx}`}
                        className="px-4 py-3 border-b border-r border-zinc-300 bg-stone-200 relative"
                        onClick={() => handleColumnEditStart(colIdx, headCell)}
                      >
                        {editingColumn === colIdx ? (
                          <input
                            type="text"
                            className="block w-full text-sm capitalize bg-transparent border border-blue-500 focus:outline-none text-center"
                            value={tempColumnName}
                            onChange={handleColumnEditChange}
                            onBlur={() => handleColumnEditBlur(colIdx)}
                            onKeyDown={(e) =>
                              handleColumnEditKeyDown(e, colIdx)
                            }
                            autoFocus
                          />
                        ) : (
                          <span className="block leading-tight text-sm capitalize cursor-pointer">
                            {headCell}
                          </span>
                        )}
                      </th>
                    ))}
                  </tr>
                </thead>

                <tbody>
                  {rowsPreview.map((row, rowIdx) => (
                    <tr
                      key={`row-preview-${rowIdx}`}
                      className="hover:bg-zinc-50"
                    >
                      {/* Botão de remover linha */}
                      <td className="px-2 py-1 border-b border-r border-zinc-200 text-center">
                        <button
                          type="button"
                          onClick={() => removeRow(rowIdx)}
                          className="text-red-500 text-xs"
                        >
                          ❌
                        </button>
                      </td>

                      {row.map((cell, cellIdx) => (
                        <td
                          key={`cell-preview-${rowIdx}-${cellIdx}`}
                          className="px-2 py-1 border-b border-r border-zinc-200 overflow-hidden relative"
                        >
                          <input
                            type="text"
                            className="block leading-tighter text-sm break-words min-w-24 max-w-80 max-h-12 bg-transparent border border-[#c4c4c4] rounded-[2px] focus:outline-none"
                            value={cell || ""}
                            onChange={(e) =>
                              handleCellEdit(rowIdx, cellIdx, e.target.value)
                            }
                          />
                        </td>
                      ))}
                    </tr>
                  ))}
                  <tr>
                    <td
                      colSpan={headPreview.length + 1}
                      className="px-2 py-2 text-start"
                    >
                      <button
                        onClick={addRow}
                        className="text-green-500 text-xs"
                      >
                        ➕ Linha
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            )}
          </div>
        </div>
      ) : (
        <label
          htmlFor={id}
          className={twMerge(
            "w-full h-full p-4",
            "rounded-lg cursor-pointer",
            "flex flex-col items-center justify-center",
            "group",
            "min-w-64 min-h-40",
            "border-dashed border-2 border-zinc-300 bg-zinc-100",
            "text-zinc-400 hover:text-zinc-600"
          )}
          {...(onDropAttrs as any)}
        >
          Arraste um arquivo aqui ou clique para selecionar
        </label>
      )}
    </div>
  );
}
