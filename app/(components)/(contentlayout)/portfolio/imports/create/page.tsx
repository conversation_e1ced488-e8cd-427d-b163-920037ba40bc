import { IconNewPortfolio } from '@/shared/layout-components/icons';
import PageHeading from '@/shared/layout-components/page-heading/PageHeading';
import UploadPortfolioForm from './_components/UploadPortfolioForm';
import { twMerge } from 'tailwind-merge';
import { Metadata } from 'next';

export default function PortfolioImportCreatePage() {
  return (
    <div className={twMerge('PortfolioPage')}>
      <PageHeading icon={<IconNewPortfolio />} primary='Adicionar Portfólio' />

      <UploadPortfolioForm />
    </div>
  );
}

export const metadata: Metadata = {
  title: 'Adicionar Portfólio',
  description:
    'Selecione um arquivo CSV com seus contatos alvo e faça o upload para iniciar as negociações',
};
