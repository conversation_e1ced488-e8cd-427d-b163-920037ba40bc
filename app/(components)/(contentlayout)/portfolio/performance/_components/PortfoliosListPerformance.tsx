"use client";
import { useEffect, useState } from "react";
import Link from "next/link";
import { PortfolioDonutChart } from "../_components/PortfolioDonutChart";
import { SelectFilter } from "@/shared/components/SelectFilter";
import {
  getAllPortfolioPerformance,
  PortfolioPerformance,
} from "@/shared/_services/portfolio.provider";
import { PortfolioExecutionStatus } from "@/shared/_services/portfolio.provider";

export function PortfoliosListPerformance() {
  const [statusFilter, setStatusFilter] = useState<string>("EXECUTING");
  const [portfolioFilter, setPortfolioFilter] = useState<string>("");
  const [portfoliosList, setPortfoliosList] = useState<PortfolioPerformance[]>(
    []
  );
  const [portfolioAllItemsList, setPortfolioAllItemsList] = useState<
    PortfolioPerformance[]
  >([]);

  const portfolioStatusList = Object.values(PortfolioExecutionStatus);

  const statusLabels: { [key: string]: string } = {
    EXECUTING: "Executando",
    QUEUED: "Em fila",
    PAUSED: "Pausado",
    WAITING: "Aguardando",
    CANCELLED: "Cancelado",
    INBOUND_EXECUTING: 'Executando Inbound',
    FINISHED: "Finalizado",
  };

  const handleSelectStatus = (status: string) => {
    setStatusFilter(status);

    applyFilters(portfolioFilter, status);
  };

  const handleSelectPortfolio = (portfolioId: string) => {
    setPortfolioFilter(portfolioId);
    applyFilters(portfolioId, statusFilter);
  };

  const applyFilters = (portfolioFilter?: string, statusFilter?: string) => {
    console.log(portfolioFilter, statusFilter);
    const filteredPortfolios = portfolioAllItemsList.filter((item) => {
      const matchesCurrentStatus = statusFilter
        ? item.executionStatus === statusFilter
        : true;
      const matchesPortfolioId = portfolioFilter
        ? item.portfolioId === portfolioFilter
        : true;

      return matchesCurrentStatus && matchesPortfolioId;
    });
    setPortfoliosList(filteredPortfolios);
  };

  useEffect(() => {
    getAllPortfolioPerformance().then((allPortfolios) => {
      setPortfoliosList(allPortfolios);
      setPortfolioAllItemsList(allPortfolios);
    });
  }, []);

  const filteredPortfolios = portfoliosList.filter((portfolio) =>
    statusFilter === "EXECUTING"
      ? portfolio.executionStatus === "EXECUTING"
      : true
  );

  return (
    <>
      <div className="grid grid-cols-12 gap-x-6 mb-2">
        <div className="sm:col-span-4 md:col-span-3">
          <SelectFilter
            label="Portfolio"
            name="portfolio"
            selectedOption={portfolioFilter}
            list={portfolioAllItemsList.map((item) => ({
              key: item.portfolioId,
              value: item.portfolioId,
              label: item.portfolioName,
            }))}
            handleSelect={handleSelectPortfolio}
          />
        </div>

        <div className="sm:col-span-4 md:col-span-3">
          <SelectFilter
            label="Status"
            name="status"
            selectedOption={statusFilter}
            list={portfolioStatusList.map((item) => ({
              key: item,
              value: item,
              label: statusLabels[item],
            }))}
            handleSelect={handleSelectStatus}
            disabled={portfolioFilter !== ""}
          />
        </div>
      </div>
      {filteredPortfolios.length === 0 && portfolioAllItemsList.length > 0 ? (
        <p className="text-base w-full">
          Nenhum portfólio encontrado com os filtros selecionados.
        </p>
      ) : (
        <div className="px-0">
          <ul className="text-base grid md:grid-cols-3 gap-6 mt-6">
            {filteredPortfolios.map((portfolio) => {
              return (
                <li key={portfolio.portfolioId} className="w-max-full">
                  <Link href={`/portfolio/performance/${portfolio.portfolioId}`}>
                    <PortfolioDonutChart
                      name={portfolio.portfolioName}
                      chartData={portfolio}
                      executionStatusLabel={statusLabels[portfolio.executionStatus]}
                    />
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </>
  );
}
