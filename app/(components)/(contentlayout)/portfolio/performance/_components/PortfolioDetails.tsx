import {
  Portfolio,
  PortfolioPerformance,
} from "@/shared/_services/portfolio.provider";
import {
  IconPortfolioExecutionStatusEnum,
  IconNegotiation,
} from "@/shared/layout-components/icons";
import PageHeading from "@/shared/layout-components/page-heading/PageHeading";
import Link from "next/link";
import { twMerge } from "tailwind-merge";
import { formatDateSimple } from "@/shared/_utils/formatters/formatDate";
import { TopNumbersBox } from "../_components/TopNumbersBox";
import { PortfolioDonutChart } from "../_components/PortfolioDonutChart";
import { DetailsList } from "@/shared/components/DetailsList";
import PortfolioActions from "./PortfolioActions";

type Props = {
  portfolio: Portfolio;
  portfolioPerformance: PortfolioPerformance;
};
export function PortfolioDetails({ portfolio, portfolioPerformance }: Props) {
  const executionStatusTranslation: { [key: string]: string } = {
    EXECUTING: "Executando",
    QUEUED: "Em Fila",
    PAUSED: "Pausado",
    WAITING: "Aguardando",
    CANCELLED: "Cancelado",
    INBOUND_EXECUTING: "Executando Inbound",
    FINISHED: "Finalizado",
  };

  const details = [
    {
      key: "originalFile",
      label: "Arquivo Importado",
      value: portfolio.originalFileName,
    },
    {
      key: "status",
      label: "Status",
      value: executionStatusTranslation[portfolio.executionStatus],
    },
    {
      key: "createdAt",
      label: "Criado em",
      value: formatDateSimple(portfolio.createdAt, "both"),
    },
    {
      key: "updatedAt",
      label: "Atualizado em",
      value: formatDateSimple(portfolio.updatedAt, "both"),
    },
  ];

  const performanceSucceedVsFailed = {
    ...portfolioPerformance,
    stats: Object.fromEntries(
      Object.entries(portfolioPerformance.stats).filter(([status]) =>
        ["SUCCEED", "FAILED"].includes(status)
      )
    ),
  } as PortfolioPerformance;

  const performanceStoppedByAdmin = {
    ...portfolioPerformance,
    stats: Object.fromEntries(
      Object.entries(portfolioPerformance.stats).filter(([status]) =>
        ["PAUSED", "CANCELLED", "UNLINKED"].includes(status)
      )
    ),
  } as PortfolioPerformance;

  const IconStatusPerf =
    IconPortfolioExecutionStatusEnum[portfolio.executionStatus];

  return (
    <div className={twMerge("PortfolioPerformancePage", "pb-4")}>
      <div className="flex flex-col gap-6 mb-8">
        <PageHeading
          className="w-full px-0 pb-0"
          icon={<IconStatusPerf />}
          primary={portfolio.name}
        />

        <div
          className={twMerge(
            "flex w-full",
            "flex-row flex-wrap justify-end gap-2 mb-0"
          )}
        >
          <PortfolioActions portfolio={portfolio} />

          <Link
            href={`/negotiation?portfolio=${portfolio.id}`}
            className="ti-btn ti-btn-success-full ti-btn-wave"
          >
            <IconNegotiation className="me-1 text-lg" />
            Ver Negociações
          </Link>
        </div>

        <DetailsList details={details} />
      </div>
      {portfolio && (
        <TopNumbersBox
          portfolio={portfolio}
          portfolioPerformance={portfolioPerformance}
        />
      )}
      <div className="grid md:grid-cols-3 gap-6 mt-6">
        <PortfolioDonutChart
          name={"Todos Status"}
          chartData={portfolioPerformance}
          executionStatusLabel={
            executionStatusTranslation[portfolio.executionStatus]
          }
        />
        <PortfolioDonutChart
          name={"Negociações Sucesso x Falha"}
          chartData={performanceSucceedVsFailed}
          executionStatusLabel={
            executionStatusTranslation[portfolio.executionStatus]
          }
        />
        <PortfolioDonutChart
          name={"Negociações Com Intervenção"}
          chartData={performanceStoppedByAdmin}
          executionStatusLabel={
            executionStatusTranslation[portfolio.executionStatus]
          }
        />
      </div>
    </div>
  );
}
