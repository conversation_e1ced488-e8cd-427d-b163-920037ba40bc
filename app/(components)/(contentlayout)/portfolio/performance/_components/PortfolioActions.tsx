"use client";

import {
  Port<PERSON>lio,
  PortfolioExecutionStatus,
  setPortfolioStatusToCancelled,
  setPortfolioStatusToDeleted,
  setPortfolioStatusToExecuting,
  setPortfolioStatusToPaused,
} from "@/shared/_services/portfolio.provider";
import {  useRouter } from "next/navigation";
import { twMerge } from "tailwind-merge";
import { ColorPortfolioExecutionStatusEnum } from "@/shared/layout-components/icons";

type Props = {
  portfolio: Portfolio;
};

export default function PortfolioActions({ portfolio }: Props) {
  const router = useRouter();
  const handleUpdatePortfolioStatus = async (
    status: PortfolioExecutionStatus
  ) => {
    if (status === PortfolioExecutionStatus.EXECUTING) {
      await setPortfolioStatusToExecuting(portfolio.id);
    } else if (status === PortfolioExecutionStatus.PAUSED) {
      await setPortfolioStatusToPaused(portfolio.id);
    } else if (status === PortfolioExecutionStatus.CANCELLED) {
      await setPortfolioStatusToCancelled(portfolio.id);
    }

    window.location.reload();
  };

  const handleDeletePortfolio = async () => {
    await setPortfolioStatusToDeleted(portfolio.id);
    router.push("/portfolio/imports/");
  };

  return (
    <div className="flex flex-row flex-wrap justify-end gap-2 mb-0">
      {[
        PortfolioExecutionStatus.EXECUTING,
        PortfolioExecutionStatus.INBOUND,
      ].includes(portfolio.executionStatus) && (
        <>
          <button
            onClick={() =>
              handleUpdatePortfolioStatus(PortfolioExecutionStatus.PAUSED)
            }
            className={twMerge(
              "ti-btn ti-btn-wave",
              "text-white",
              ColorPortfolioExecutionStatusEnum[PortfolioExecutionStatus.PAUSED]
                .bg
            )}
          >
            Pausar Execução
          </button>
          <button
            onClick={() =>
              handleUpdatePortfolioStatus(PortfolioExecutionStatus.CANCELLED)
            }
            className={twMerge(
              "ti-btn ti-btn-wave",
              "text-white",
              ColorPortfolioExecutionStatusEnum[
                PortfolioExecutionStatus.CANCELLED
              ].bg
            )}
          >
            Cancelar Execução
          </button>
        </>
      )}
      {[
        PortfolioExecutionStatus.PAUSED,
        PortfolioExecutionStatus.QUEUED,
      ].includes(portfolio.executionStatus) && (
        <button
          onClick={() =>
            handleUpdatePortfolioStatus(PortfolioExecutionStatus.EXECUTING)
          }
          className={twMerge(
            "ti-btn ti-btn-wave",
            "text-white",
            ColorPortfolioExecutionStatusEnum[PortfolioExecutionStatus.WAITING]
              .bg
          )}
        >
          Iniciar Execução
        </button>
      )}
      {[
        PortfolioExecutionStatus.CANCELLED,
        PortfolioExecutionStatus.FINISHED,
      ].includes(portfolio.executionStatus) && (
        <button
          onClick={handleDeletePortfolio}
          className={twMerge(
            "ti-btn ti-btn-wave",
            "text-white",
            ColorPortfolioExecutionStatusEnum[PortfolioExecutionStatus.CANCELLED]
              .bg
          )}
        >
          Excluir
        </button>
      )}
    </div>
  );
}
