import {
  IconNegotiation,
  IconSuccess,
  IconCustomer,
  IconPortfolioExecutionStatusEnum,
} from "@/shared/layout-components/icons";
import { formatNumber } from "@/shared/_utils/formatters/formatNumber";
import {
  Portfolio,
  PortfolioPerformance,
} from "@/shared/_services/portfolio.provider";

const IconQueue = IconPortfolioExecutionStatusEnum["QUEUED"];

export function TopNumbersBox({
  portfolio,
  portfolioPerformance,
}: {
  portfolio: Portfolio;
  portfolioPerformance: PortfolioPerformance;
}) {
  return (
    <div className="grid ">
      <div className="box custom-box">
        <div className="box-body !p-0">
          <div className="grid grid-cols-12 gap-x-6">
            <div className="xl:col-span-3 col-span-12 border-e border-dashed dark:border-defaultborder/10">
              <div className="flex flex-wrap items-start p-6">
                <div className="me-4 leading-none">
                  <span className="avatar avatar-md !rounded-full !bg-primary shadow-sm">
                    <i className="ti ti-package text-[1.125rem] text-white ">
                      <IconCustomer />
                    </i>
                  </span>
                </div>
                <div className="flex-grow">
                  <h5 className="font-semibold ">
                    {formatNumber(portfolio.processedQuantity ?? 0, 0)}
                  </h5>
                  <p className="text-[#8c9097] dark:text-white/50 mb-0 text-[10.5px]">
                    Total Contatos
                  </p>
                </div>
              </div>
            </div>
            <div className="xl:col-span-3 col-span-12 border-e border-dashed dark:border-defaultborder/10">
              <div className="flex flex-wrap items-start p-6">
                <div className="me-3 leading-none">
                  <span className="avatar avatar-md !rounded-full bg-secondary shadow-sm">
                    <i className="ti ti-rocket text-[1.125rem] text-white">
                      <IconNegotiation />
                      {/* <MdTranscribe/> */}
                    </i>
                  </span>
                </div>
                <div className="flex-grow">
                  <h5 className="font-semibold ">
                    {portfolioPerformance.stats.IN_PROGRESS ?? 0}
                  </h5>
                  <p className="text-[#8c9097] dark:text-white/50 mb-0 text-[10.5px]">
                    Em Andamento
                  </p>
                </div>
              </div>
            </div>
            <div className="xl:col-span-3 col-span-12 border-e border-dashed dark:border-defaultborder/10">
              <div className="flex flex-wrap items-start p-6">
                <div className="me-3 leading-none">
                  <span className="avatar avatar-md !rounded-full bg-success shadow-sm">
                    <i className="ti ti-wallet text-[1.125rem] text-white">
                      <IconQueue />
                    </i>
                  </span>
                </div>
                <div className="flex-grow">
                  <h5 className="font-semibold ">
                    {portfolioPerformance.stats.PENDING ?? 0}
                  </h5>
                  <p className="text-[#8c9097] dark:text-white/50 mb-0 text-[10.5px]">
                    Em Fila
                  </p>
                </div>
              </div>
            </div>
            <div className="xl:col-span-3 col-span-12 ">
              <div className="flex flex-wrap items-start p-6">
                <div className="me-3 leading-none">
                  <span className="avatar avatar-md !rounded-full bg-warning shadow-sm">
                    <i className="ti ti-packge-import text-[1.125rem] text-white">
                      <IconSuccess />
                    </i>
                  </span>
                </div>
                <div className="flex-grow">
                  <h5 className="font-semibold ">
                    {portfolioPerformance.stats.SUCCEED ?? 0}
                  </h5>
                  <p className="text-[#8c9097] dark:text-white/50 mb-0 text-[10.5px]">
                    Concluídas com Sucesso
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
