"use client";
import { twMerge } from "tailwind-merge";
import dynamic from "next/dynamic";
import { PortfolioPerformance } from "@/shared/_services/portfolio.provider";
import { PortfolioItemStatus } from "@/shared/_services/portfolio-item.provider";
import { ApexOptions } from "apexcharts";
import { IconPortfolioExecutionStatusEnum } from "@/shared/layout-components/icons";
import React from "react";

const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

type spark3 = {
  options?: ApexOptions;
  width?: number;
  height?: string | number;
  series?: ApexOptions["series"];
  label?: XAxisAnnotations;
  color?: string | string[] | (string & string[]) | undefined;
  endingShape?: string;
  enabled?: boolean;
};

type Props = {
  className?: string;
  name: string;
  chartData: PortfolioPerformance;
  executionStatusLabel?: string;
};
export function PortfolioDonutChart({
  className,
  name,
  chartData,
  executionStatusLabel,
}: Props) {
  const statusLabels: { [key: string]: string } = {
    PENDING: "Em fila",
    IN_PROGRESS: "Em andamento",
    PAUSED: "Pausado",
    SUCCEED: "Concluído",
    FAILED: "Falhou",
    CANCELLED: "Cancelado",
    UNLINKED: "Desvinculado",
    IDLE: "Inativo",
    FOLLOWED_UP: "Follow up",
    SCHEDULED_FOLLOW_UP: "Follow up agendado",
    OPTED_OUT: "Não perturbe",
    FINISHED: "Finalizado",
  };

  const statusColors: { [key: string]: string } = {
    PENDING: "#60169d",
    IN_PROGRESS: "#046180",
    PAUSED: "#cd9700",
    SUCCEED: "#32CD32",
    FAILED: "#FF6347",
    CANCELLED: "#8B0000",
    UNLINKED: "#808080",
    IDLE: "#808080",
    FOLLOWED_UP: "#4682B4",
    SCHEDULED_FOLLOW_UP: "#4682B4",
    OPTED_OUT: "#FF4500",
    FINISHED: "#279356",
  };

  // Extracting data for the chart
  const statuses = Object.keys(chartData.stats) as PortfolioItemStatus[];
  const series = statuses.map((status) => chartData.stats[status]) as
    | ApexAxisChartSeries
    | ApexNonAxisChartSeries
    | undefined;
  const labels = statuses.map((status) => statusLabels[status] || status);

  const baseOptions: spark3 = {
    series,
    options: {
      chart: {
        type: "donut" as const,
        sparkline: {
          enabled: true,
        },
        dropShadow: {
          enabled: true,
          enabledOnSeries: undefined,
          top: 0,
          left: 0,
          blur: 1,
          // color: "#fff",
          opacity: 0.05,
        },
      },
      legend: {
        show: true,
        position: "bottom" as const,
        onItemHover: {
          highlightDataSeries: true,
        },
        labels: {
          useSeriesColors: false,
        },
      },
      labels,
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 280,
              height: 200,
            },
          },
        },
      ],
      colors: statuses.map((status) => statusColors[status] || "#000000"),
    },
  };

  return (
    <div
      className={twMerge(
        "PortfolioPerformanceWidget h-full w-full flex justify-center",
        className
      )}
    >
      {chartData && baseOptions.series && baseOptions.series.length > 0 ? (
        <div className="box mb-0 flex flex-col justify-between w-full h-full">
          <div className="box-header min-h-4">
            <div className="box-title">{name}</div>
            <p className="text-[10px] flex items-center">
              {IconPortfolioExecutionStatusEnum[chartData.executionStatus] && (
                <span className="mr-1" style={{ fontSize: "1.5em" }}>
                  {React.createElement(
                    IconPortfolioExecutionStatusEnum[chartData.executionStatus]
                  )}
                </span>
              )}
              {executionStatusLabel}
            </p>
          </div>
          <div className="flex items-center justify-center p-4 h-full">
            <ReactApexChart
              options={baseOptions.options}
              series={baseOptions.series}
              type="donut"
              width="100%"
              height={250}
            />
          </div>
        </div>
      ) : (
        <div className="box mb-0 flex flex-col justify-between w-full h-full">
          <div className="box-header min-h-4">
            <div className="box-title">{name}</div>
          </div>
          <div className="flex items-center justify-center p-4 h-full">
            <span>Sem dados disponíveis</span>
          </div>
        </div>
      )}
    </div>
  );
}
