import { IconPortfolioPerformance } from '@/shared/layout-components/icons';
import PageHeading from '@/shared/layout-components/page-heading/PageHeading';
import { twMerge } from 'tailwind-merge';
import { PortfoliosListPerformance } from './_components/PortfoliosListPerformance';
import { Metadata } from 'next';

export default function PortfolioPerformancePage() {
  return (
    <div className={twMerge('PortfolioPerformancePage')}>
      <PageHeading
        icon={<IconPortfolioPerformance />}
        primary='Portfólio Performance'
        className='px-0'
      />
      <PortfoliosListPerformance />
    </div>
  );
}

export const metadata: Metadata = {
  title: 'Portfolios Performance',
  description: '',
};
