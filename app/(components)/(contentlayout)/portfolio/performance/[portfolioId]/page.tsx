import { Metadata } from 'next';
import {
  getPortfolio,
  getPortfolioPerformance,
} from '@/shared/_services/portfolio.provider';

import { PortfolioDetails } from '../_components/PortfolioDetails';

type PortfolioPerformancePageProps = {
  params: Promise<{ portfolioId: string }>;
};

export default async function PortfolioPerformanceItemPage({
  params,
}: PortfolioPerformancePageProps) {
  const portfolioId = (await params).portfolioId;
  const portfolio = await getPortfolio(portfolioId);
  const portfolioPerformance = await getPortfolioPerformance(portfolioId);

  return (
    <PortfolioDetails
      portfolio={portfolio}
      portfolioPerformance={portfolioPerformance}
    />
  );
}

export const metadata: Metadata = {
  title: 'Portfolio Performance Details',
  description: '',
};
