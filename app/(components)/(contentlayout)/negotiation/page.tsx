import { Metada<PERSON> } from "next";
import PageHeading from "@/shared/layout-components/page-heading/PageHeading";
import { NegotiationsList } from "./_components/NegotiationsList";
import { IconNegotiation } from "@/shared/layout-components/icons";

export default function PortfolioItemNegotiationPage() {
  return (
    <div className="PortfolioItemNegotiationPage">
      <PageHeading primary="Negociações" icon={<IconNegotiation />} />

      <NegotiationsList />
    </div>
  );
}

export const metadata: Metadata = {
  title: "Negociações",
  description:
    "Ongoing exchange of messages between the workflow operator and the people listed in a portfolio",
};
