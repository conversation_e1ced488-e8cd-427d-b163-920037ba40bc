"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useSearchParams, useRouter } from "next/navigation";
import { Pagination } from "antd";
import { useQuery } from "@tanstack/react-query";
import { twMerge } from "tailwind-merge";
import TcdInput from "@/shared/form-components/tcd-input/TcdInput";
import { formatDateSimple } from "@/shared/_utils/formatters/formatDate";
import { SelectFilter } from "@/shared/components/SelectFilter";
import {
  ColorPortfolioItemStatusEnum,
  IconPortfolioItemStatusEnum,
} from "@/shared/layout-components/icons";
import { formatPhoneNumber } from "@/shared/_utils/formatters/formatNumber";
import {
  getAllPortfolioItems,
  IPortfolioItem,
  PortfolioItemStatus,
} from "@/shared/_services/portfolio-item.provider";
import {
  getAllPortfolios,
  Portfolio,
} from "@/shared/_services/portfolio.provider";

type Props = {
  className?: string;
};

type Pagination = {
  current: number;
  pageSize: number;
  total?: number;
  totalPages?: number;
};

export function NegotiationsList({ className, ...restProps }: Props) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [hasInteractionInput, setHasInteractionInput] = useState<string>(
    searchParams.get("hasInteraction") || ""
  );
  const [hasInteractionFilter, setHasInteractionFilter] = useState<string>(
    searchParams.get("hasInteraction") || ""
  );

  const [waitingBusinessUserResponseFilter, setWaitingBusinessUserResponse] =
    useState<string>(searchParams.get("waitingBusinessUserResponse") || "");

  const [
    waitingBusinessUserResponseInput,
    setWaitingBusinessUserResponseInput,
  ] = useState<string>(searchParams.get("waitingBusinessUserResponse") || "");

  const [negotiations, setNegotiations] = useState<IPortfolioItem[]>([]);

  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);

  const [statusFilter, setStatusFilter] = useState<string>(
    searchParams.get("status") || ""
  );
  const [portfolioFilter, setPortfolioFilter] = useState<string>(
    searchParams.get("portfolio") || ""
  );
  const [phoneFilter, setPhoneFilter] = useState<string>(
    searchParams.get("phoneNumber") || ""
  );
  const [page, setPage] = useState<number>(
    parseInt(searchParams.get("page") || "1", 10)
  );

  const [statusInput, setStatusInput] = useState<string>(statusFilter);
  const [portfolioInput, setPortfolioInput] = useState<string>(portfolioFilter);
  const [phoneInput, setPhoneInput] = useState<string>(phoneFilter);

  const [pageSize, setPageSize] = useState<number>(
    parseInt(searchParams.get("limit") || "10", 10)
  );

  useEffect(() => {
    async function loadPortfolios() {
      const data = await getAllPortfolios();
      setPortfolios(data);
    }
    loadPortfolios();
  }, []);

  const updateUrl = (
    newParams: Record<string, string | number | undefined>
  ) => {
    const params = new URLSearchParams(searchParams.toString());

    Object.entries(newParams).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        params.set(key, String(value));
      } else {
        params.delete(key);
      }
    });

    router.push(`?${params.toString()}`);
  };
  const { data: response, isLoading } = useQuery({
    queryKey: [
      "negotiations",
      statusFilter,
      portfolioFilter,
      phoneFilter,
      hasInteractionFilter,
      waitingBusinessUserResponseFilter,
      page,
      pageSize,
    ],
    queryFn: async () => {
      const filters: Record<string, any> = {};

      if (statusFilter) filters.currentStatus = statusFilter;
      if (portfolioFilter) filters.portfolioId = portfolioFilter;
      if (phoneFilter) filters.phoneNumber = phoneFilter;
      if (waitingBusinessUserResponseFilter !== "") {
        filters.waitingBusinessUserResponse =
          waitingBusinessUserResponseFilter === "true";
      }
      if (hasInteractionFilter !== "") {
        filters.lastInteraction =
          hasInteractionFilter === "true" ? { not: null } : null;
      }

      const response = await getAllPortfolioItems(
        filters,
        { page, limit: pageSize },
        portfolios
      );
      return response;
    },
    refetchOnWindowFocus: true,
    staleTime: 0,
  });

  useEffect(() => {
    if (response) {
      console.log("API Response:", response);
      setNegotiations(response.items);
    }
  }, [response]);

  const handleFilter = () => {
    updateUrl({
      status: statusInput,
      portfolio: portfolioInput,
      phoneNumber: phoneInput,
      hasInteraction: hasInteractionInput,
      waitingBusinessUserResponse: waitingBusinessUserResponseInput,
      page: 1,
    });
    setStatusFilter(statusInput);
    setPortfolioFilter(portfolioInput);
    setPhoneFilter(phoneInput);
    setHasInteractionFilter(hasInteractionInput);
    setWaitingBusinessUserResponse(waitingBusinessUserResponseInput);
    setPage(1);
  };

  const onPageChange = (newPage: number, newPageSize?: number) => {
    const targetPage = newPageSize && newPageSize !== pageSize ? 1 : newPage;
    const targetSize = newPageSize ?? pageSize;

    setPage(targetPage);
    setPageSize(targetSize);
    updateUrl({
      page: targetPage,
      limit: targetSize,
      status: statusFilter,
      portfolio: portfolioFilter,
      phoneNumber: phoneFilter,
    });
  };

  const statusOptions: { key: string; label: string; value: string }[] = [
    {
      key: "PENDING",
      label: "Pendente",
      value: "PENDING",
    },
    {
      key: "IN_PROGRESS",
      label: "Em Andamento",
      value: "IN_PROGRESS",
    },
    {
      key: "PAUSED",
      label: "Pausado",
      value: "PAUSED",
    },
    {
      key: "SUCCEED",
      label: "Concluído",
      value: "SUCCEED",
    },
    {
      key: "FAILED",
      label: "Falhou",
      value: "FAILED",
    },
    {
      key: "CANCELLED",
      label: "Cancelado",
      value: "CANCELLED",
    },
    {
      key: "UNLINKED",
      label: "Desvinculado",
      value: "UNLINKED",
    },
    {
      key: "IDLE",
      label: "Inativo",
      value: "IDLE",
    },
    {
      key: "FOLLOWED_UP",
      label: "Follow up",
      value: "FOLLOWED_UP",
    },
    {
      key: "SCHEDULED_FOLLOW_UP",
      label: "Follow up agendado",
      value: "SCHEDULED_FOLLOW_UP",
    },
    {
      key: "OPTED_OUT",
      label: "Não perturbe",
      value: "OPTED_OUT",
    },
    {
      key: "FINISHED",
      label: "Finalizado",
      value: "FINISHED",
    },
  ];

  const waitingBusinessUserResponseOptions: {
    key: string;
    label: string;
    value: string;
  }[] = [
    {
      key: "false",
      label: "Não",
      value: "false",
    },
    {
      key: "true",
      label: "Sim",
      value: "true",
    },
  ];

  const hasInteractionOptions: {
    key: string;
    label: string;
    value: string;
  }[] = [
    {
      key: "false",
      label: "Não",
      value: "false",
    },
    {
      key: "true",
      label: "Sim",
      value: "true",
    },
  ];

  return (
    <div className="px-1 md:px-4 pt-4 grid grid-cols-12 gap-6 negotiations-grid">
      <div className="md:col-span-3">
        <div className="box h-full m-0!">
          <div className="box-header">
            <label className="box-title">Buscar por Telefone</label>
          </div>
          <div className="box-body">
            <TcdInput
              name="phone"
              id="phone"
              type="number"
              value={phoneInput}
              placeholder="Exemplo: 11933334444"
              inputClassName="border-gray-200! rounded-sm text-sm"
              onChange={(e) => setPhoneInput(e.target.value)}
              onInput={(e) => {
                const input = e.target as HTMLInputElement;
                input.value = input.value.replace(/\D/g, "");
                setPhoneInput(input.value);
              }}
              onPaste={(e) => {
                e.preventDefault();
                const text = e.clipboardData.getData("text");
                const onlyNumbers = text.replace(/\D/g, "");
                setPhoneInput(onlyNumbers);
              }}
            />
          </div>
        </div>
      </div>

      <div className="md:col-span-3">
        <SelectFilter
          label="Portfolios"
          name="portfolio"
          list={portfolios.map((item) => ({
            label: item.name,
            key: item.id,
            value: item.id,
          }))}
          selectedOption={portfolioInput}
          handleSelect={(value) => setPortfolioInput(value)}
        />
      </div>

      <div className="md:col-span-2">
        <SelectFilter
          label="Status"
          name="negotiationStatus"
          list={Object.values(PortfolioItemStatus).map((status) => {
            const option = statusOptions.find((item) => item.value === status);
            return {
              key: status,
              label: option ? option.label : status,
              value: status,
            };
          })}
          selectedOption={statusInput}
          handleSelect={(value) => setStatusInput(value)}
        />
      </div>

      <div className="md:col-span-2">
        <SelectFilter
          label="Teve Interação"
          name="hasInteractionFilter"
          list={hasInteractionOptions}
          selectedOption={hasInteractionInput}
          handleSelect={(value) => setHasInteractionInput(value)}
        />
      </div>

      <div className="md:col-span-2">
        <SelectFilter
          label="Aguardando Operator"
          name="waitingBusinessUserResponseFilter"
          list={waitingBusinessUserResponseOptions}
          selectedOption={waitingBusinessUserResponseInput}
          handleSelect={(value) => setWaitingBusinessUserResponseInput(value)}
        />
      </div>

      <div className="col-start-11 col-span-2">
        <button
          type="submit"
          className="ti-btn ti-btn-wave ti-btn-success-full w-full mb-0"
          onClick={() => handleFilter()}
          style={{ backgroundColor: "#111c8a" }}
        >
          Filtrar
        </button>
      </div>

      <div className="box col-span-12">
        <div
          {...restProps}
          className={twMerge(
            "NegotiationsList",
            "custom box",
            "min-h-[85vh] md:min-h-[70vh] overflow-auto justify-between",
            className
          )}
        >
          <div className="table-responsive">
            <table className="table whitespace-nowrap table-hover min-w-full ti-custom-table-hover table-striped table-bordered">
              <thead>
                <tr className="border-b border-defaultborder">
                  <th scope="col" className="text-start">
                    Telefone
                  </th>
                  <th scope="col" className="text-start">
                    Portfólio
                  </th>
                  <th scope="col" className="text-start">
                    Última Mensagem Recebida
                  </th>
                  <th scope="col" className="text-start">
                    Aguardando Operador
                  </th>
                  <th scope="col" className="text-start">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody>
                {isLoading && (
                  <tr className="border-b border-defaultborder">
                    <td className="!p-0" colSpan={6}>
                      <p className="p-3 w-full h-full block leading-none text-center pt-6 pb-12 text-base">
                        Loading Negotiations...
                      </p>
                    </td>
                  </tr>
                )}
                {negotiations.length > 0
                  ? negotiations.map((negItem) => {
                      const IconPortfItemStatus =
                        IconPortfolioItemStatusEnum[negItem.currentStatus];
                      const statusColor =
                        ColorPortfolioItemStatusEnum[negItem.currentStatus];

                      return (
                        <tr
                          className="border-b border-defaultborder"
                          key={negItem.id}
                        >
                          <td className="!p-0">
                            <Link
                              className="p-3 w-full h-full block hover:underline cursor-pointer hover:text-secondary leading-none"
                              href={`/negotiation/${negItem.id}`}
                            >
                              {formatPhoneNumber(negItem.phoneNumber)}
                            </Link>
                          </td>
                          <td className="!p-0">
                            {negItem.portfolioId && (
                              <Link
                                className="p-3 w-full h-full block hover:underline cursor-pointer hover:text-primary leading-none"
                                href={`/portfolio/performance/${negItem.portfolioId}`}
                              >
                                {negItem.portfolioName}
                              </Link>
                            )}
                          </td>
                          <td>
                            {formatDateSimple(negItem.lastInteraction, "both")}
                          </td>

                          <td>
                            {negItem.waitingBusinessUserResponse
                              ? "Sim"
                              : "Não"}
                          </td>
                          <td className="!py-1">
                            <div className="flex items-center gap-4">
                              <div
                                className={twMerge(
                                  "avatar avatar-md avatar-rounded",
                                  statusColor.text,
                                  statusColor.bg
                                )}
                              >
                                <IconPortfItemStatus />
                              </div>
                              <p
                                className={twMerge(
                                  "capitalize leading-none text-sm",
                                  statusColor.text
                                )}
                              >
                                {
                                  statusOptions.find(
                                    (item) =>
                                      item.value === negItem.currentStatus
                                  )?.label
                                }
                              </p>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  : !isLoading && (
                      <tr className="border-b border-defaultborder">
                        <td className="!p-0" colSpan={6}>
                          <p className="p-3 w-full h-full block leading-none text-center pt-6 pb-12 text-base">
                            - Nenhum resultado encontrado para os filtros
                            aplicados -
                          </p>
                        </td>
                      </tr>
                    )}
              </tbody>
            </table>
          </div>
          <Pagination
            align="center"
            defaultCurrent={1}
            current={page}
            pageSize={pageSize}
            total={response?.total || 0}
            onChange={onPageChange}
            showSizeChanger
            style={{ marginTop: "16px", marginBottom: "16px" }}
          />
        </div>
      </div>
    </div>
  );
}
