import {
  IPortfolioItem,
  PortfolioItemStatus,
} from '@/shared/_services/portfolio-item.provider';
import {
  ColorPortfolioItemStatusEnum,
  IconNegotiation,
  IconPortfolioItemStatusEnum,
} from '@/shared/layout-components/icons';
import PageHeading from '@/shared/layout-components/page-heading/PageHeading';
import Link from 'next/link';
import { twMerge } from 'tailwind-merge';

import {
  formatDateAgoFactory,
  formatDateSimple,
} from '@/shared/_utils/formatters/formatDate';
import { ReactNode } from 'react';

export interface DetailOfAnEntity {
  label: ReactNode;
  value?: ReactNode;
  secondary?: ReactNode;
  href?: string;
  className?: string;
  altValue?: ReactNode;
}

const fmtDateAgo = formatDateAgoFactory({ style: 'long', maxParts: 2 });

export function PortfolioItemDetails({
  negData,
  negotiationId,
}: {
  negData: IPortfolioItem;
  negotiationId: string;
}) {
  const heading = negData.phoneNumber
    ? negData.phoneNumber
    : negotiationId;

 
  const portfStatus: PortfolioItemStatus =
    negData.currentStatus as PortfolioItemStatus;

  const currStatus = (negData.currentStatus || '')
    .toLowerCase()
    .replaceAll('_', ' ');

  const IconPortfItemStatus = IconPortfolioItemStatusEnum[portfStatus];
  const statusColor = ColorPortfolioItemStatusEnum[portfStatus];

  const details: DetailOfAnEntity[] = [
    {
      label: 'Status',
      value: negData.currentStatus,
      secondary: (
        <IconPortfItemStatus
          className={twMerge('text-base', statusColor.text)}
        />
      ),
    },
    {
      label: 'Created At',
      value: fmtDateAgo(negData.createdAt),
      altValue: formatDateSimple(negData.createdAt, 'MMM DD'),
    },
    {
      label: 'Last Update',
      value: fmtDateAgo(negData.updatedAt),
      altValue: formatDateSimple(negData.updatedAt, 'MMM DD'),
    },
    {
      label: 'Last Interaction',
      value: fmtDateAgo(negData.updatedAt),
      altValue: formatDateSimple(negData.updatedAt, 'MMM DD'),
    },
    {
      label: 'Import Status',
      value: currStatus,
      className: `${statusColor.text} font-semibold`,
    },
  ];

  const IconPause = IconPortfolioItemStatusEnum[PortfolioItemStatus.PAUSED];
  const IconCancel = IconPortfolioItemStatusEnum[PortfolioItemStatus.CANCELLED];
  const IconProgress =
    IconPortfolioItemStatusEnum[PortfolioItemStatus.IN_PROGRESS];
  return (
    <div className={twMerge('PortfolioItemViewPage')}>
      <div
        className='grid grid-cols-3 gap-2'
        data-bolota='flex flex-wrap md:flex-row'
      >
        {/*  */}

        <div className={twMerge('col-span-3 md:col-span-2 row-span-1')}>
          <PageHeading
            icon={<IconNegotiation />}
            primary={`Negotiation with ${heading}`}
          />
        </div>

        <div
          className={twMerge(
            // "w-full md:w-1/3",
            'inline-flex',
            'flex-col items-start gap-2',
            'pt-4 pe-4',
            // "p-4",
            // "order-2 md:order-12",
            'col-span-3 md:col-span-1 row-span-1 md:row-span-5',
          )}
        >
          <Link
            href='/negotiation'
            className='ti-btn ti-btn-outline-dark ti-btn-wave'
          >
            <IconNegotiation className='me-1 text-lg' />
            All Negotiations
          </Link>

          <button
            className={twMerge(
              'ti-btn ti-btn-wave',
              ColorPortfolioItemStatusEnum[PortfolioItemStatus.PAUSED].text,
              ColorPortfolioItemStatusEnum[PortfolioItemStatus.PAUSED].bg,
            )}
          >
            <IconPause className='me-1 text-lg' />
            Pause Negotiation
          </button>
          <button
            className={twMerge(
              'ti-btn ti-btn-wave',
              ColorPortfolioItemStatusEnum[PortfolioItemStatus.CANCELLED].text,
              ColorPortfolioItemStatusEnum[PortfolioItemStatus.CANCELLED].bg,
            )}
          >
            <IconCancel className='me-1 text-lg' />
            Cancel Negotiation
          </button>
          <button
            className={twMerge(
              'ti-btn ti-btn-wave',
              ColorPortfolioItemStatusEnum[PortfolioItemStatus.PAUSED].text,
              ColorPortfolioItemStatusEnum[PortfolioItemStatus.PAUSED].bg,
            )}
          >
            <IconProgress className='me-1 text-lg' />
            Re-start Negotiation
          </button>

          {/* <BtnDeletePortfolio portfolioId={portfolioId} /> */}

          {/* {portfolio.importStatus === PortfolioImportStatus.SUCCESS && (
            <Link href={`/portfolio/performance/${portfolioId}`} className="ti-btn ti-btn-primary ti-btn-wave">
              <IconPortfolioPerformance className="me-1 text-lg" />
              Portfolio Performance
            </Link>
          )} */}
        </div>

        <div
          className={twMerge(
            // "w-full md:w-2/3",
            // "inline-block",
            // "px-4 pt-4",
            // "p-4",
            // "order-3 md:order-2",
            'col-span-3 md:col-span-2 row-span-1',
          )}
        >
          <ul className='list-none max-w-screen-sm'>
            {details.map((detail, detailIdx) => {
              const cls = detail.className || '';

              let miolo = (
                <>
                  <div className='md:flex items-center md:text-left py-2 md:py-0'>
                    <p className='font-semibold md:font-medium leading-tighter md:leading-normal uppercase md:normal-case md:w-36 me-4 md:text-right text-[0.65rem] md:text-sm'>
                      {detail.label}:
                    </p>
                    <div className='flex items-center'>
                      {detail.altValue ? (
                        <>
                          <p
                            className={twMerge(
                              'opacity-85 group-hover:hidden',
                              cls,
                            )}
                          >
                            {detail.value}
                          </p>
                          <p
                            className={twMerge(
                              'opacity-85 hidden group-hover:block',
                              cls,
                            )}
                          >
                            {detail.altValue}
                          </p>
                        </>
                      ) : (
                        <p className={twMerge('opacity-85', cls)}>
                          {detail.value}
                        </p>
                      )}
                      {detail.secondary && (
                        <p className='ms-2 inline-block text-[0.7rem] capitalize'>
                          {detail.secondary}
                        </p>
                      )}
                    </div>
                  </div>
                </>
              );
              if (detail.href) {
                miolo = (
                  <Link
                    href={detail.href}
                    className='hover:underline hover:text-secondary/70'
                  >
                    {miolo}
                  </Link>
                );
              }

              return (
                <li
                  className={twMerge(
                    'default px-2 -mx-2 group',
                    !detail.href && 'hover:bg-primary/15',
                  )}
                  key={detailIdx}
                >
                  {miolo}
                </li>
              );
            })}
          </ul>
        </div>

        {/*  */}
      </div>
    </div>
  );
}
