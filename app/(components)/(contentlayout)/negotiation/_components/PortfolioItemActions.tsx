"use client";
import {
  IPortfolioItem,
  PortfolioItemStatus,
  setPortfolioItemStatusToCancel,
  setPortfolioItemStatusToInProgress,
  setPortfolioItemStatusToPause,
  setPortfolioItemStatusToSucceed,
  setPortfolioItemStatusToFinished,
} from "@/shared/_services/portfolio-item.provider";
import { IconSettings } from "@/shared/layout-components/icons";

type Props = {
  portfolioItem: IPortfolioItem | undefined;
};

export default function PortfolioItemActions({ portfolioItem }: Props) {
  const handleUpdatePortfolioItemStatus = async (status: PortfolioItemStatus) => {
    if (!portfolioItem) return;

    if (status === PortfolioItemStatus.IN_PROGRESS) {
      await setPortfolioItemStatusToInProgress(portfolioItem.id);
    } else if (status === PortfolioItemStatus.PAUSED) {
      await setPortfolioItemStatusToPause(portfolioItem.id);
    } else if (status === PortfolioItemStatus.CANCELLED) {
      await setPortfolioItemStatusToCancel(portfolioItem.id);
    } else if (status === PortfolioItemStatus.SUCCEED) {
      await setPortfolioItemStatusToSucceed(portfolioItem.id);
    } else if (status === PortfolioItemStatus.FINISHED) {
      await setPortfolioItemStatusToFinished(portfolioItem.id);
    }

    window.location.reload();
  };

  return (
    <div className="hs-dropdown ti-dropdown">
      <button
        aria-label="button"
        className="ti-btn ti-btn-icon ti-btn-secondary text-secondary"
        type="button"
        aria-expanded="false"
      >
        <IconSettings />
      </button>
      <ul className="hs-dropdown-menu ti-dropdown-menu hidden">
        <li>
          {portfolioItem?.currentStatus !== undefined &&
            [PortfolioItemStatus.PAUSED].includes(
              portfolioItem.currentStatus
            ) && (
              <button
                onClick={() =>
                  handleUpdatePortfolioItemStatus(PortfolioItemStatus.IN_PROGRESS)
                }
                className="ti-dropdown-item !py-2 !px-[0.9375rem] !text-[0.8125rem] !font-medium block"
              >
                Iniciar
              </button>
            )}
        </li>
        <li>
          {portfolioItem?.currentStatus !== undefined &&
            [
              PortfolioItemStatus.UNLINKED, 
            ].includes(portfolioItem.currentStatus) && (
              <button
                onClick={() =>
                  handleUpdatePortfolioItemStatus(PortfolioItemStatus.SUCCEED)
                }
                className="ti-dropdown-item !py-2 !px-[0.9375rem] !text-[0.8125rem] !font-medium block"
              >
                Concluir
              </button>
            )}
        </li>
        <li>
          {portfolioItem?.currentStatus !== undefined &&
            [
              PortfolioItemStatus.UNLINKED, 
              PortfolioItemStatus.IN_PROGRESS,
            ].includes(portfolioItem.currentStatus) && (
              <button
                onClick={() =>
                  handleUpdatePortfolioItemStatus(PortfolioItemStatus.FINISHED)
                }
                className="ti-dropdown-item !py-2 !px-[0.9375rem] !text-[0.8125rem] !font-medium block"
              >
                Encerrar Atendimento
              </button>
            )}
        </li>
        <li>
          {portfolioItem?.currentStatus !== undefined &&
            ![
              PortfolioItemStatus.CANCELLED, 
              PortfolioItemStatus.FINISHED, 
              PortfolioItemStatus.SUCCEED, 
            ].includes(portfolioItem.currentStatus) && (
              <button
                onClick={() =>
                  handleUpdatePortfolioItemStatus(PortfolioItemStatus.CANCELLED)
                }
                className="ti-dropdown-item !py-2 !px-[0.9375rem] !text-[0.8125rem] !font-medium block"
              >
                Cancelar
              </button>
            )}
        </li>
        <li>
          {portfolioItem?.currentStatus !== undefined &&
            [
              PortfolioItemStatus.CANCELLED, 
              PortfolioItemStatus.FINISHED, 
              PortfolioItemStatus.SUCCEED, 
            ].includes(portfolioItem.currentStatus) && (
              <button
                onClick={() => {}}
                className="ti-dropdown-item !py-2 !px-[0.9375rem] !text-[0.8125rem] !font-medium block"
              >
                Nenhuma ação disponível
              </button>
            )}
        </li>
      </ul>
    </div>
  );
}
