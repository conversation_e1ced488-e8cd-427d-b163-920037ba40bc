"use client";
import { use, useEffect, useState } from "react";
import PerfectScrollbar from "react-perfect-scrollbar";
import { twMerge } from "tailwind-merge";
import {
  getConversationHistory,
  getPortfolioItem,
  IMessageHistory,
  IPortfolioItem,
  PortfolioItemStatus,
  sendMessage,
  downloadConversationFile,
  downloadAudioFile,
  portfolioItemStatusTranslation,
} from "@/shared/_services/portfolio-item.provider";
import "react-perfect-scrollbar/dist/css/styles.css";
import { Collapse } from "antd";
import { IconSendPlane } from "@/shared/layout-components/icons";
import PortfolioItemActions from "../_components/PortfolioItemActions";
import { formatDateSimple } from "@/shared/_utils/formatters/formatDate";
import { formatPhoneNumber } from "@/shared/_utils/formatters/formatNumber";

export default function Chat({
  params,
}: {
  params: Promise<{ negotiationId: string }>;
}) {
  const [negotiationData, setNegotiationData] = useState<IPortfolioItem>();
  const [conversationHistory, setConversationHistory] = useState<
    IMessageHistory[]
  >([]);
  const [messageText, setMessageText] = useState<string>(""); // Add state for message text
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [audioBlobMap, setAudioBlobMap] = useState<Record<string, string>>({});

  const { negotiationId } = use(params);
  useEffect(() => {
    getPortfolioItem(negotiationId).then((negotiationResponse) => {
      setNegotiationData(negotiationResponse);

      getConversationHistory(negotiationResponse.id).then(
        async (conversationHistory) => {
          setConversationHistory(conversationHistory);

          const audiosToDownload = conversationHistory.filter(
            (m) =>
              m.fileUrl &&
              (m.messageType === "audio/mpeg" || m.messageType === "AUDIO")
          );

          const entries = await Promise.all(
            audiosToDownload.map(async (msg) => {
              const blob = new Blob(
                [await downloadAudioFile(negotiationResponse.id, msg.fileUrl!)],
                {
                  type: "audio/mpeg",
                }
              );
              const blobUrl = URL.createObjectURL(blob);
              return { id: msg.id, blobUrl };
            })
          );

          const newMap: Record<string, string> = {};
          entries.forEach(({ id, blobUrl }) => {
            newMap[id] = blobUrl;
          });

          setAudioBlobMap(newMap);
        }
      );
    });

    return () => {
      Object.values(audioBlobMap).forEach((blobUrl) => {
        URL.revokeObjectURL(blobUrl);
      });
    };
  }, [negotiationId]);

  const getMessageType = (file: File | null) => {
    if (!file) return "TEXT";
    return file.type || "application/octet-stream";
  };

  const handleSendMessage = async () => {
    const messageType = getMessageType(selectedFile);

    const conversationHistoryResponse = await sendMessage(
      negotiationData!.id,
      messageText,
      selectedFile || undefined,
      messageType
    );
    setConversationHistory(conversationHistoryResponse);

    const negotiationResponse = await getPortfolioItem(negotiationId);
    setNegotiationData(negotiationResponse);

    setMessageText("");
    setSelectedFile(null);
  };

  const handleDownload = async (fileUrl: string) => {
    try {
      const fileName = fileUrl.split("/").pop();

      const blob = await downloadConversationFile(negotiationId, fileName!);
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = fileName!;
      document.body.appendChild(a);
      a.click();
      a.remove();
    } catch (error: any) {
      console.log("error", error);
    }
  };

  function formatLabel(key: string) {
    return key
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (l) => l.toUpperCase());
  }

  return (
    <div className={twMerge("WidgetsPage")}>
      <div className="main-chart-wrapper p-2 gap-2 lg:flex">
        <PerfectScrollbar
          className={twMerge(
            "chat-user-details border dark:border-defaultborder/10 open",
            "relative block"
          )}
          id="chat-user-details"
        >
          <div className="flex items-center justify-between w-full p-4 border-b dark:border-defaultborder/10">
            <div>
              <h5 className="font-semibold mb-0 text-[1.25rem] !text-defaulttextcolor dark:text-defaulttextcolor/70">
                Negociação
              </h5>
            </div>
            <PortfolioItemActions portfolioItem={negotiationData} />
          </div>
          <div className="text-center mb-[3rem]">
            <p className="mb-1 text-[0.9375rem] font-semibold text-defaulttextcolor dark:text-defaulttextcolor/70 leading-none chatnameperson ">
              {negotiationData?.contactName}
            </p>
            <p className="text-[0.75rem] text-[#8c9097] dark:text-white/50 !mb-4">
              <span className="chatnameperson">
                {negotiationData?.phoneNumber
                  ? formatPhoneNumber(negotiationData.phoneNumber)
                  : ""}
              </span>
            </p>
          </div>
          <div className="mb-[3rem]">
            <ul className="shared-files list-none">
              <li className="!mb-4">
                <div className="flex items-center">
                  <div className="flex-grow">
                    <p className="mb-0 text-[#8c9097] dark:text-white/50">
                      Telefone
                    </p>
                    <p className="text-[0.75rem] font-semibold mb-0 dark:text-defaulttextcolor/70 ">
                      {negotiationData?.phoneNumber
                        ? formatPhoneNumber(negotiationData.phoneNumber)
                        : ""}
                    </p>
                  </div>
                </div>
              </li>
              <li className="!mb-4">
                <div className="flex items-center">
                  <div className="flex-grow">
                    <p className="mb-0 text-[#8c9097] dark:text-white/50">
                      Status
                    </p>
                    <p className="text-[0.75rem] font-semibold mb-0 dark:text-defaulttextcolor/70 ">
                      {negotiationData?.currentStatus &&
                        portfolioItemStatusTranslation[negotiationData.currentStatus]}
                    </p>
                  </div>
                </div>
              </li>
              <li className="!mb-4">
                <div className="flex items-center">
                  <div className="flex-grow">
                    <p className="mb-0 text-[#8c9097] dark:text-white/50">
                      Última Interação
                    </p>
                    <p className="text-[0.75rem] font-semibold mb-0 dark:text-defaulttextcolor/70 ">
                      {negotiationData?.lastInteraction
                        ? formatDateSimple(negotiationData.lastInteraction)
                        : ""}
                    </p>
                  </div>
                </div>
              </li>
              <li className="!mb-4">
                <div className="flex items-center">
                  <div className="flex-grow">
                    <p className="mb-0 text-[#8c9097] dark:text-white/50">
                      Aguardando Resposta Interação
                    </p>
                    <p className="text-[0.75rem] font-semibold mb-0 dark:text-defaulttextcolor/70 ">
                      {negotiationData?.waitingBusinessUserResponse
                        ? "Sim"
                        : "Não"}
                    </p>
                  </div>
                </div>
              </li>
              <li className="!mb-4">
                <div className="flex items-center">
                  <div className="flex-grow">
                    <p className="mb-0 text-[#8c9097] dark:text-white/50">
                      Insights
                    </p>
                    <p className="text-[0.75rem] font-semibold mb-0 dark:text-defaulttextcolor/70 ">
                      {negotiationData?.insights
                        ? negotiationData?.insights
                        : "Não há insights"}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
            {negotiationData?.customData && (
              <Collapse className="mb-4" ghost>
                <Collapse.Panel
                  header="Mais dados do cliente"
                  key="1"
                  className="font-medium"
                >
                  <div className="space-y-2">
                    {Object.entries(negotiationData.customData).map(
                      ([key, value]) => (
                        <div
                          key={key}
                          className="flex justify-between border-b py-1"
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "0.5rem",
                          }}
                        >
                          <span className="text-[0.75rem] mb-0 text-[#8c9097] dark:text-white/50">
                            {formatLabel(key)}
                          </span>
                          <span className="text-[0.75rem] font-semibold mb-0 dark:text-defaulttextcolor/70">
                            {String(value)}
                          </span>
                        </div>
                      )
                    )}
                  </div>
                </Collapse.Panel>
              </Collapse>
            )}
          </div>
        </PerfectScrollbar>
        <div className="main-chat-area border dark:border-defaultborder/10">
          <PerfectScrollbar style={{ height: "750px" }}>
            <div className="chat-content" id="main-chat-content">
              <ul className="list-none">
                <li className="chat-day-label">
                  <span>Hoje</span>
                </li>
                {conversationHistory.map((message, index) => (
                  <li
                    key={index}
                    className={`chat-item-${message.role === "assistant" || message.role === "attendant" ? "start" : "end"}`}
                  >
                    <div className="chat-list-inner">
                      <div
                        className={
                          message.role === "assistant" ||
                          message.role === "attendant"
                            ? "ms-4"
                            : "me-3"
                        }
                      >
                        <div className="main-chat-msg">
                          <div>
                            {message.fileUrl && (
                              <div className="mb-2">
                                {message.messageType === "audio/mpeg" ||
                                (message.messageType === "AUDIO" &&
                                  audioBlobMap[message.id]) ? (
                                  <audio
                                    controls
                                    src={audioBlobMap[message.id]}
                                    style={{
                                      width: "280px",
                                      height: "40px",
                                      backgroundColor: "white",
                                      borderRadius: "8px",
                                    }}
                                  >
                                    Seu navegador não suporta o player de áudio.
                                  </audio>
                                ) : (
                                  <button
                                    onClick={() =>
                                      handleDownload(message.fileUrl!)
                                    }
                                    className="ml-2 text-primary hover:text-primary-dark"
                                    title="Download arquivo"
                                  >
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="32"
                                      height="32"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    >
                                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                      <polyline points="7 10 12 15 17 10" />
                                      <line x1="12" y1="15" x2="12" y2="3" />
                                    </svg>
                                    <span className="text-sm text-gray-600 block mt-1">
                                      {message.fileUrl?.split("/").pop()}
                                    </span>
                                  </button>
                                )}
                              </div>
                            )}

                            {message.messageText && (
                              <>
                                <p className="mb-0">{message.messageText}</p>
                              </>
                            )}
                          </div>
                        </div>
                        <span className="flex items-end gap-[5px] max-h-[20px] chatting-user-info">
                          <span
                            className="msg-sent-time"
                            style={{ margin: "0" }}
                          >
                            &nbsp;
                            {message.role === "assistant" && (
                              <p className="text-xs text-gray-500 mt-1">
                                {message.sent
                                  ? `Enviado em ${formatDateSimple(message.sent_at)}`
                                  : message.time_to_go
                                    ? `Agendada para ${formatDateSimple(message.time_to_go)}`
                                    : ""}
                              </p>
                            )}
                            {message.role === "attendant" && (
                              <p className="text-xs text-gray-500 mt-1">
                                {message.sent
                                  ? `Enviado em ${formatDateSimple(message.sent_at)}`
                                  : message.time_to_go
                                    ? `Agendada para ${formatDateSimple(message.time_to_go)}`
                                    : ""}
                              </p>
                            )}
                            {message.role === "user" && (
                              <p className="text-xs text-gray-500 mt-1">
                                {message.createdAt
                                  ? ` ${formatDateSimple(message.createdAt)}`
                                  : ""}
                              </p>
                            )}
                          </span>
                          {message.role === "assistant" ? (
                            <span className="chatnameperson">
                              {" "}
                              • Digai Negocia IA
                            </span>
                          ) : message.role === "attendant" ? (
                            <span className="chatnameperson"> • Operador</span>
                          ) : (
                            " • Contato"
                          )}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </PerfectScrollbar>
          {negotiationData?.currentStatus !== undefined &&
            ![
              PortfolioItemStatus.PAUSED,
              PortfolioItemStatus.CANCELLED,
            ].includes(negotiationData.currentStatus) && (
              <div className="chat-footer">
                <div className="flex items-center w-full gap-2">
                  <input
                    className="form-control w-full !rounded-md"
                    placeholder="Escreva uma mensagem aqui..."
                    type="text"
                    name="messageText"
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                  />
                  {selectedFile && (
                    <div className="mt-2 text-sm text-gray-600">
                      {selectedFile.name}
                      <button
                        className="ml-2 text-red-500 hover:text-red-700"
                        onClick={() => setSelectedFile(null)}
                      >
                        ×
                      </button>
                    </div>
                  )}
                  <input
                    type="file"
                    className="hidden"
                    id="fileInput"
                    accept="application/pdf"
                    onChange={(e) =>
                      setSelectedFile(e.target.files?.[0] || null)
                    }
                  />
                  <label
                    htmlFor="fileInput"
                    className="ti-btn bg-gray-200 text-gray-700 ti-btn-icon"
                    aria-label="Anexar arquivo"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48" />
                    </svg>
                  </label>
                  <button
                    aria-label="button"
                    type="button"
                    className="ti-btn bg-primary text-white ti-btn-icon ti-btn-send"
                    onClick={() => handleSendMessage()}
                  >
                    <IconSendPlane />
                  </button>
                </div>
              </div>
            )}
        </div>
      </div>
    </div>
  );
}
