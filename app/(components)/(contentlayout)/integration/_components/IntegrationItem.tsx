'use client';

import { IIntegrationInfo } from '@/shared/_services/integration.provider';
import {
  IconConnect,
  IconIntegrationManage,
} from '@/shared/layout-components/icons';
import Link from 'next/link';
import { ChangeEventHandler, PropsWithChildren, useState } from 'react';
import { twMerge } from 'tailwind-merge';

export interface IIntegrationItemProps
  extends Partial<PropsWithChildren<JSX.IntrinsicElements['div']>> {
  integration: IIntegrationInfo;
}

export default function IntegrationItem(props: IIntegrationItemProps) {
  const { className, integration, ...restProps } = props;

  const [isDisabled, setIsDisabled] = useState(false);

  const idIntg = integration.id;
  const nameIntg = integration.serviceName || '';
  const descIntg = integration.description || '';
  const isActive = Boolean(integration.isActive);

  const handleIsActiveChange: ChangeEventHandler<HTMLInputElement> = (evt) => {
    if (isActive) {
      setIsDisabled(!isDisabled);
    }
  };

  const IconAction = isActive ? IconIntegrationManage : IconConnect;
  const colorOutline = isActive
    ? isDisabled
      ? 'outline-warning'
      : 'outline-primary'
    : 'outline-secondary';
  const colorText = isActive
    ? isDisabled
      ? 'text-warning'
      : 'text-primary'
    : '';
  const colorBtn = isActive ? 'ti-btn-primary-full' : 'ti-btn-secondary-full';
  const textAction = isActive ? 'Manage' : 'Setup';

  return (
    <div
      className={twMerge(
        'box !mb-0',
        isActive && 'outline outline-2',
        colorOutline,
      )}
    >
      <div
        {...restProps}
        className={twMerge(
          'IntegrationItem',
          // "flex items-center justify-start gap-4 py-2 my-2 border-b border-defaultborder",
          'box-body',
          className,
        )}
      >
        <div className='flex items-center justify-start gap-4 mb-2'>
          <p
            className={twMerge('text-xl mb-1 font-semibold me-auto', colorText)}
          >
            {nameIntg}
          </p>

          <div className='px-2'>
            <div className='custom-toggle-switch'>
              <input
                id={idIntg}
                type='checkbox'
                name={idIntg}
                checked={isActive && !isDisabled}
                onChange={handleIsActiveChange}
                disabled
              />
              <label htmlFor={idIntg} className='label-success mb-1'></label>
            </div>
          </div>
        </div>

        <p className='mb-0 text-slate-800 dark:text-white/60 text-sm'>
          {descIntg}
        </p>
      </div>

     
    </div>
  );
}
