import { Metadata } from 'next';
import { getIntegrations } from '@/shared/_services/integration.provider';
import { IconIntegration } from '@/shared/layout-components/icons';
import PageHeading from '@/shared/layout-components/page-heading/PageHeading';
import { twMerge } from 'tailwind-merge';
import IntegrationItem from './_components/IntegrationItem';

export default async function IntegrationPage() {
  const listIntegrationsResponse = await getIntegrations();
  const availableIntegrations = listIntegrationsResponse.data;

  const content =
    availableIntegrations && availableIntegrations?.length > 0 ? (
      <div className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4'>
        {availableIntegrations.map((integration) => {
          return (
            <IntegrationItem key={integration.id} integration={integration} />
          );
        })}
      </div>
    ) : (
      <div className='text-muted'>No integrations available</div>
    );

  return (
    <div className={twMerge('IntegrationPage')}>
      <PageHeading primary='Integrations' icon={<IconIntegration />} />

      <div className='px-1 md:px-4'>{content}</div>
    </div>
  );
}

export const metadata: Metadata = {
  title: 'Integrations',
  description: 'Setup and Manage integrations with external services',
};
