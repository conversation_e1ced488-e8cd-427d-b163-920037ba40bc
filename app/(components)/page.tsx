"use client";
import { useRouter } from "next/navigation";
import { ChangeEventHand<PERSON>, useEffect, useState } from "react";
import { RiEyeLine, RiEyeOffLine } from "react-icons/ri";

import { CollectCashLogo, FidelEasyLogo, SalesZapLogo } from "@/public/assets";
import { Fetch } from "@/_interceptors";
import getEnv from "@/shared/_utils/hooks/getEnv";
import { getCustomer } from "@/shared/_services/customer.provider";

type AuthData = {
  user: string;
  pass: string;
};

const defaultAuthData: AuthData = {
  user: "",
  pass: "",
};

export default function Home() {
  useEffect(() => {}, []);

  const [showPass, setShowPass] = useState(false);
  const [error, setError] = useState("");
  const [data, setData] = useState<AuthData>(defaultAuthData);
  const [isLoading, setIsLoading] = useState(false);
  const { user, pass } = data;

  const handleSetData: ChangeEventHandler<HTMLInputElement> = (evt: any) => {
    setData({ ...data, [evt.target.name]: evt.target.value });
    setError("");
  };

  const router = useRouter();
  const RouteChange = () => {
    const path = "/dashboard";
    router.push(path);
  };

  const handleLoginSubmit = async (evt: any) => {
    evt.preventDefault();

    setIsLoading(true);

    try {
      localStorage.clear();
      sessionStorage.clear();
      const fetch = Fetch();

      if (
        getEnv("SERVICE_MOCK_RESPONSE") &&
        user === "<EMAIL>" &&
        pass === "password@123"
      ) {
        const costumer = getCustomer();
        localStorage.setItem("customer", JSON.stringify(costumer));
        RouteChange();

        return;
      }

      const data = await fetch.post(
        `${process.env.NEXT_PUBLIC_TRANSCENDENCE_SERVICE_URL}/v1/auth/session/login`,
        { email: user, password: pass }
      );
      localStorage.setItem("customer", JSON.stringify(data));
      RouteChange();
    } catch (err) {
      if (err instanceof Error) {
        console.error("Error during login:", err.message);
        setError(err.message);
      } else {
        console.error("Error during login:", err);
        setError("An error occurred during login. Please try again.");
      }

      setIsLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="flex justify-center authentication authentication-basic h-full text-defaultsize text-defaulttextcolor">
        <div className="grid grid-cols-12">
          <div className="xxl:col-span-4 xl:col-span-4 lg:col-span-4 md:col-span-3 sm:col-span-2"></div>
          <div className="xxl:col-span-4 xl:col-span-4 lg:col-span-4 md:col-span-6 sm:col-span-8 col-span-12">
            <div style={{ height: "28vw", maxHeight: "calc(50vh - 25rem)" }} />

            <div className="box !p-[3rem] transition-all duration-300">
              <nav
                className="!block px-6  mx-auto firebase-data"
                aria-label="Tabs"
                role="tablist"
              >
                <div className="flex justify-center space-x-2 bg-light p-2 rounded-md rtl:space-x-reverse">
                  <button
                    type="button"
                    className="hs-tab-active:bg-[#E0C560] hs-tab-active:text-white py-1 px-1 inline-flex items-center gap-2 bg-transparent text-sm font-medium text-center text-gray-500 rounded-sm hover:text-primary  dark:text-white/70 dark:hover:text-white"
                    id="segment-item-1"
                    data-hs-tab="#segment-01"
                    aria-controls="segment-01"
                  >
                    <FidelEasyLogo favicon />
                  </button>
                  <button
                    type="button"
                    className="hs-tab-active:bg-primary hs-tab-active:text-white py-1 px-1 inline-flex items-center gap-2 bg-transparent text-sm font-medium text-center text-gray-500 rounded-sm hover:text-primary  dark:text-white/70 dark:hover:text-white active"
                    id="segment-item-2"
                    data-hs-tab="#segment-02"
                    aria-controls="segment-02"
                  >
                    <CollectCashLogo favicon />
                  </button>
                  <button
                    type="button"
                    className="hs-tab-active:bg-[#BA911E] hs-tab-active:text-white py-1 px-1 inline-flex items-center gap-2 bg-transparent text-sm font-medium text-center text-gray-500 rounded-sm hover:text-primary  dark:text-white/70 dark:hover:text-white"
                    id="segment-item-2"
                    data-hs-tab="#segment-03"
                    aria-controls="segment-03"
                  >
                    <SalesZapLogo favicon />
                  </button>
                </div>
              </nav>

              <div
                className="box-body hidden"
                role="tabpanel"
                id="segment-01"
                aria-labelledby="segment-item-1"
              >
                <div className="min-h-[26rem] min-w-[14rem] flex justify-center items-center text-muted text-base">
                  Not Available
                </div>
              </div>

              <div
                className="box-body hidden"
                role="tabpanel"
                id="segment-03"
                aria-labelledby="segment-item-3"
              >
                <div className="min-h-[26rem] min-w-[14rem] flex justify-center items-center text-muted text-base">
                  Not Available
                </div>
              </div>

              <div
                className="box-body"
                role="tabpanel"
                id="segment-02"
                aria-labelledby="segment-item-2"
              >
                <p className="h5 font-semibold mb-2 text-center">Sign In</p>

                <form onSubmit={handleLoginSubmit}>
                  <div className="grid grid-cols-12 gap-y-4">
                    <div className="xl:col-span-12 col-span-12">
                      <label
                        htmlFor="signin-user"
                        className="form-label text-default"
                      >
                        User
                      </label>
                      <input
                        type="email"
                        name="user"
                        className="form-control form-control-lg w-full !rounded-md"
                        id="user"
                        onChange={handleSetData}
                        value={user}
                        placeholder="User"
                        required
                      />
                    </div>

                    <div className="xl:col-span-12 col-span-12 mb-2">
                      <label
                        htmlFor="signin-pass"
                        className="form-label text-default block"
                      >
                        Pass
                      </label>
                      <div className="input-group">
                        <input
                          name="pass"
                          type={showPass ? "text" : "password"}
                          value={pass}
                          onChange={handleSetData}
                          className="form-control form-control-lg !rounded-s-md !border-l-[1px] !border-r-[0px]"
                          id="signin-pass"
                          placeholder="Password"
                          required
                        />
                        <button
                          onClick={() => setShowPass(!showPass)}
                          aria-label="button"
                          className="ti-btn ti-btn-light !rounded-s-none !mb-0"
                          style={{
                            border: "1px solid rgb(var(--input-border))",
                            borderInlineStart: "none",
                          }}
                          type="button"
                          id="button-addon2"
                        >
                          {showPass ? (
                            <RiEyeLine className="align-middle" />
                          ) : (
                            <RiEyeOffLine className="align-middle" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div className="xl:col-span-12 col-span-12 grid mt-2">
                      <button
                        type="submit"
                        className="ti-btn ti-btn-primary !bg-primary !text-white !font-medium"
                        style={{ height: "40px" }}
                      >
                        {isLoading ? (
                          <span
                            className="animate-spin inline-block w-4 h-4 border-[3px] border-current border-t-transparent text-white rounded-full"
                            role="status"
                            aria-label="loading"
                          >
                            <span className="sr-only">Loading...</span>
                          </span>
                        ) : (
                          <>Sign In</>
                        )}
                      </button>
                    </div>
                  </div>
                </form>

                {error && (
                  <div
                    className="p-4 my-4 bg-danger/40 text-sm border border-danger text-danger/90 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
                    role="alert"
                  >
                    {error}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="xxl:col-span-4 xl:col-span-4 lg:col-span-4 md:col-span-3 sm:col-span-2"></div>
        </div>
      </div>
    </div>
  );
}
