'use client';
import React, { useEffect, useContext } from 'react';
import { connect } from 'react-redux';
import { ThemeChanger } from '../../shared/redux/action';
import { Initialload } from '../../shared/contextapi';
import * as switcherdata from '../../shared/data/switcherdata/switcherdata';

function Layout({
  children,
  local_varaiable,
  ThemeChanger,
}: {
  children: React.ReactNode;
  local_varaiable: any;
  ThemeChanger: any;
}) {
  const theme: any = useContext(Initialload);

  useEffect(() => {
    if (typeof window !== 'undefined' && !theme.pageloading) {
      // console.log("gdgggg",!theme.pageloading)
      switcherdata.LocalStorageBackup(ThemeChanger, theme.setpageloading);
    }
  }, []);

  return (
    <main className={`${local_varaiable.body ? local_varaiable.body : ''}`}>
      {theme.pageloading && children}
    </main>
  );
}

const mapStateToProps = (state: any) => ({
  local_varaiable: state,
});
export default connect(mapStateToProps, { ThemeChanger })(Layout);
