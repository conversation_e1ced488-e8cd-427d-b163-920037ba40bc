'use client';
import './globals.scss';
import React, { useState } from 'react';
import { Provider } from 'react-redux';
import { Initialload } from '../shared/contextapi';
import '@/public/assets/css/custom/custom-styles.css';
import store from '../shared/redux/store';
import PrelineScript from './PrelineScript';
import QueryProvider from '../shared/_context/query.context';
import AuthProvider from '../shared/_context/auth.context';

export default function RootLayout({ children }: any) {
  const [pageloading, setpageloading] = useState(false);

  const local_varaiable: any = store.getState();

  const customstyles: any = {
    ...(local_varaiable?.colorPrimaryRgb !== '' && {
      '--primary-rgb': local_varaiable.colorPrimaryRgb,
    }),
    ...(local_varaiable?.colorPrimary !== '' && {
      '--primary': local_varaiable.colorPrimary,
    }),
    ...(local_varaiable?.darkBg !== '' && {
      '--dark-bg': local_varaiable.darkBg,
    }),
    ...(local_varaiable?.bodyBg !== '' && {
      '--body-bg': local_varaiable.bodyBg,
    }),
    ...(local_varaiable?.inputBorder !== '' && {
      '--input-border': local_varaiable.inputBorder,
    }),
    ...(local_varaiable?.Light !== '' && { '--light': local_varaiable.Light }),
  };

  return (
    <Provider store={store}>
      <html
        suppressHydrationWarning={true}
        dir={local_varaiable.dir}
        className={local_varaiable.class}
        data-header-styles={local_varaiable.dataHeaderStyles}
        data-vertical-style={local_varaiable.dataVerticalStyle}
        data-nav-layout={local_varaiable.dataNavLayout}
        data-menu-styles={local_varaiable.dataMenuStyles}
        data-toggled={local_varaiable.dataToggled}
        data-nav-style={local_varaiable.dataNavStyle}
        data-hor-style={local_varaiable.horStyle}
        data-page-style={local_varaiable.dataPageStyle}
        data-width={local_varaiable.dataWidth}
        data-menu-position={local_varaiable.dataMenuPosition}
        data-header-position={local_varaiable.dataHeaderPosition}
        data-icon-overlay={local_varaiable.iconOverlay}
        data-bg-img={local_varaiable.bgImg}
        data-icon-text={local_varaiable.iconText}
        //Styles
        style={customstyles}
      >
        <head>
          <meta
            name='viewport'
            content='width=device-width, initial-scale=1, maximum-scale=10, minimum-scale=0.001, user-scalable=1, interactive-widget=resizes-content'
          />
          <meta name='theme-color' content='#063067' />
          {/* <link rel="icon" type="image/svg+xml" href="/favicon.svg?v=2" /> */}
        </head>
        <QueryProvider>
          <AuthProvider>
            <Initialload.Provider value={{ pageloading, setpageloading }}>
              <body
                className={`${local_varaiable.body ? local_varaiable.body : ''}`}
              >
                {children}
              </body>
            </Initialload.Provider>
            <PrelineScript />
          </AuthProvider>
        </QueryProvider>
      </html>
    </Provider>
  );
}
