@import "../public/assets/css/style.css";

// @import "simplebar-react/dist/simplebar.min.css";

@media (max-width: 991.98px) {
  [data-toggled="open"] {
    #responsive-overlay {
      visibility: visible !important;
      --tw-bg-opacity: 0.5;
    }
  }
}

@media screen and (min-width: 992px) {
  [data-vertical-style="doublemenu"]
    .app-sidebar
    .slide.has-sub.active
    > .slide-menu {
    display: block !important;
  }
}

@media (max-width: 1400px) {
  .main-chart-wrapper .chat-user-details {
    position: relative;
    display: block;
  }
  .main-chart-wrapper .chat-user-details.open {
    top: 0 !important;
  }
}

@media (max-width: 992px) {
  .PortfolioItemNegotiationPage {
    margin-left: 240px;
    width: calc(100vw - 240px);
  }
}

@media (max-width: 1115px) {
  .negotiations-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
  }
  .negotiations-grid > div {
    width: 100%;
    min-width: 220px;
  }
}

@media print {
  body {
    writing-mode: horizontal-tb;
    size: landscape;
    margin: 10mm;
  }

  .page-break {
    page-break-before: always;
  }

  .avoid-break {
    page-break-inside: avoid;
    break-inside: avoid;
    margin-top: 40px;
  }

  .app-sidebar {
    display: none !important;
  }

  .width-print {
    width: 130% !important;
  }
  .print-body {
    width: 100% !important;
    height: auto !important;
    page-break-inside: avoid;
    transform: scale(0.9);
    transform-origin: top left;
  }

  #salesOverview {
    width: 60% !important;
    height: auto !important;
    page-break-inside: avoid;
    transform: scale(0.9);
    transform-origin: top left;
  }

  #salesOverview canvas {
    height: auto !important;
  }

  @media print and (orientation: portrait) {
    .print-body {
      transform: scale(0.8);
    }

    #salesOverview {
      width: 80% !important;
      transform: scale(0.8);
    }
  }
}
