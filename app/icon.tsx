import { ImageResponse } from 'next/og';
import { ReactNode } from 'react';
import { CollectCashLogo, FidelEasyLogo, SalesZapLogo } from '@/public/assets';

// Image metadata
export const size = {
  width: 32,
  height: 32,
};
export const contentType = 'image/png';

// Image generation
export default function Icon() {
  const iconSvg: { [key: string]: ReactNode } = {
    FIDELEASY: <FidelEasyLogo favicon />,
    COLLECTCASH: <CollectCashLogo favicon />,
    SALESZAP: <SalesZapLogo favicon />,
  };

  return new ImageResponse(
    (
      // ImageResponse JSX element
      <div
        style={{
          fontSize: 24,
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {iconSvg[process.env.NEXT_PUBLIC_SITE_NAME || 'COLLECTCASH']}
      </div>
    ),
    {
      // For convenience, we can re-use the exported icons size metadata
      // config to also set the ImageResponse's width and height.
      ...size,
    },
  );
}
